#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 开始设置 Vue2 + Element-UI 前端项目...\n')

// 检查 Node.js 版本
const nodeVersion = process.version
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])

console.log(`📋 当前 Node.js 版本: ${nodeVersion}`)

if (majorVersion < 14) {
  console.error('❌ 错误: 需要 Node.js 版本 >= 14.0.0')
  console.log('请升级您的 Node.js 版本后重试')
  process.exit(1)
}

console.log('✅ Node.js 版本检查通过\n')

// 检查是否已安装依赖
const packageJsonPath = path.join(__dirname, '../package.json')
const nodeModulesPath = path.join(__dirname, '../node_modules')

if (!fs.existsSync(nodeModulesPath)) {
  console.log('📦 安装项目依赖...')
  try {
    execSync('npm install', { 
      cwd: path.dirname(packageJsonPath),
      stdio: 'inherit' 
    })
    console.log('✅ 依赖安装完成\n')
  } catch (error) {
    console.error('❌ 依赖安装失败:', error.message)
    process.exit(1)
  }
} else {
  console.log('✅ 依赖已安装\n')
}

// 检查环境配置文件
const envDevPath = path.join(__dirname, '../.env.development')
const envProdPath = path.join(__dirname, '../.env.production')

if (fs.existsSync(envDevPath) && fs.existsSync(envProdPath)) {
  console.log('✅ 环境配置文件已存在\n')
} else {
  console.log('⚠️  环境配置文件缺失，请检查 .env.development 和 .env.production 文件\n')
}

console.log('🎉 项目设置完成！\n')
console.log('📝 可用的命令:')
console.log('  npm run serve    - 启动开发服务器')
console.log('  npm run build    - 构建生产版本')
console.log('  npm run lint     - 代码检查')
console.log('\n🌐 开发服务器将在 http://localhost:3000 启动')
console.log('🔗 API 代理到 http://localhost:8000/api')
console.log('\n💡 提示: 确保后端服务在 8000 端口运行')
