#!/usr/bin/env python3
"""
网页元素抓取工具快速使用示例
演示如何快速抓取网页元素并生成测试脚本
"""

from simple_web_extractor import SimpleWebExtractor

def example_1_basic_usage():
    """示例1: 基本使用"""
    print("📝 示例1: 基本使用")
    print("-" * 40)
    
    # 创建抓取器
    extractor = SimpleWebExtractor()
    
    # 抓取百度首页元素
    url = "https://www.baidu.com"
    print(f"抓取网页: {url}")
    
    elements = extractor.extract_elements(url)
    
    # 显示前5个元素
    print(f"\n前5个元素:")
    for i, element in enumerate(elements[:5], 1):
        print(f"{i}. {element['元素类型']}: {element['元素名称']}")
        print(f"   XPath: {element['XPath']}")
    
    return elements

def example_2_filter_by_type():
    """示例2: 按类型过滤元素"""
    print("\n📝 示例2: 按类型过滤")
    print("-" * 40)
    
    extractor = SimpleWebExtractor()
    elements = extractor.extract_elements("https://www.baidu.com")
    
    # 按类型分组
    type_groups = {}
    for element in elements:
        element_type = element['元素类型']
        if element_type not in type_groups:
            type_groups[element_type] = []
        type_groups[element_type].append(element)
    
    # 显示各类型元素
    for element_type, type_elements in type_groups.items():
        print(f"\n{element_type} ({len(type_elements)} 个):")
        for element in type_elements[:3]:  # 只显示前3个
            print(f"  - {element['元素名称']}: {element['XPath']}")

def example_3_generate_files():
    """示例3: 生成文件"""
    print("\n📝 示例3: 生成文件")
    print("-" * 40)
    
    extractor = SimpleWebExtractor()
    elements = extractor.extract_elements("https://httpbin.org/forms/post")
    
    if elements:
        # 生成JSON文件
        extractor.save_to_json(elements, "demo_elements.json")
        
        # 生成Robot Framework脚本
        extractor.generate_robot_script(elements, "demo_test.robot")
        
        print(f"✅ 已生成文件:")
        print(f"  - demo_elements.json (包含 {len(elements)} 个元素)")
        print(f"  - demo_test.robot (测试脚本)")

def example_4_custom_analysis():
    """示例4: 自定义分析"""
    print("\n📝 示例4: 自定义分析")
    print("-" * 40)
    
    extractor = SimpleWebExtractor()
    elements = extractor.extract_elements("https://www.baidu.com")
    
    # 统计分析
    total_elements = len(elements)
    elements_with_id = len([e for e in elements if e['ID']])
    elements_with_text = len([e for e in elements if e['文本内容']])
    
    print(f"📊 元素分析:")
    print(f"  - 总元素数: {total_elements}")
    print(f"  - 有ID的元素: {elements_with_id} ({elements_with_id/total_elements*100:.1f}%)")
    print(f"  - 有文本的元素: {elements_with_text} ({elements_with_text/total_elements*100:.1f}%)")
    
    # 找出最常见的元素类型
    type_counts = {}
    for element in elements:
        element_type = element['元素类型']
        type_counts[element_type] = type_counts.get(element_type, 0) + 1
    
    most_common = max(type_counts.items(), key=lambda x: x[1])
    print(f"  - 最常见元素类型: {most_common[0]} ({most_common[1]} 个)")

def example_5_batch_processing():
    """示例5: 批量处理多个网页"""
    print("\n📝 示例5: 批量处理")
    print("-" * 40)
    
    urls = [
        "https://www.baidu.com",
        "https://httpbin.org/forms/post"
    ]
    
    extractor = SimpleWebExtractor()
    all_results = {}
    
    for url in urls:
        print(f"\n处理: {url}")
        elements = extractor.extract_elements(url)
        all_results[url] = elements
        
        if elements:
            print(f"  ✅ 找到 {len(elements)} 个元素")
            
            # 统计元素类型
            type_counts = {}
            for element in elements:
                element_type = element['元素类型']
                type_counts[element_type] = type_counts.get(element_type, 0) + 1
            
            print(f"  📊 类型分布: {dict(type_counts)}")
        else:
            print(f"  ❌ 未找到元素")
    
    # 汇总统计
    total_elements = sum(len(elements) for elements in all_results.values())
    print(f"\n📈 批量处理汇总:")
    print(f"  - 处理网页数: {len(urls)}")
    print(f"  - 总元素数: {total_elements}")
    print(f"  - 平均每页: {total_elements/len(urls):.1f} 个元素")

def main():
    """运行所有示例"""
    print("🚀 网页元素抓取工具使用示例")
    print("=" * 60)
    
    examples = [
        example_1_basic_usage,
        example_2_filter_by_type,
        example_3_generate_files,
        example_4_custom_analysis,
        example_5_batch_processing
    ]
    
    for example_func in examples:
        try:
            example_func()
        except Exception as e:
            print(f"❌ 示例 {example_func.__name__} 执行失败: {e}")
    
    print(f"\n🎉 所有示例执行完成!")
    print("=" * 60)
    
    print(f"\n💡 使用提示:")
    print(f"1. 运行 python simple_web_extractor.py 进行交互式抓取")
    print(f"2. 运行 python web_element_extractor.py 使用完整功能")
    print(f"3. 查看生成的JSON和Robot文件了解元素详情")
    print(f"4. 根据需要修改选择器以适应特定网站")

if __name__ == "__main__":
    main()
