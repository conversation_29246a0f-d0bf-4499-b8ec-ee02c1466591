import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 全局样式
import '@/styles/index.scss'

// API服务
import api from '@/services/api'

Vue.use(ElementUI)

Vue.config.productionTip = false

// 全局属性
Vue.prototype.$api = api
Vue.prototype.$message = ElementUI.Message
Vue.prototype.$confirm = ElementUI.MessageBox.confirm

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
