# 网页元素抓取工具

一个强大的Python工具类，用于自动抓取网页中的可交互元素，包括按钮、输入框、下拉选择等。每个元素包含详细信息如类型、名称、XPath、CSS选择器等。

## 功能特性

### 🎯 核心功能
- **自动元素识别**: 智能识别网页中的可交互元素
- **多种元素类型**: 支持按钮、输入框、下拉选择、链接、复选框、单选框等
- **详细元素信息**: 提取元素类型、名称、XPath、CSS选择器、位置、大小等
- **智能命名**: 自动生成有意义的元素名称
- **XPath生成**: 自动生成可靠的XPath定位器

### 📊 数据导出
- **JSON格式**: 导出结构化的元素数据
- **Robot Framework**: 自动生成RF测试脚本
- **过滤功能**: 按条件过滤元素
- **分组统计**: 按类型分组显示元素

### 🔧 高级功能
- **自定义选择器**: 支持扩展自定义元素选择器
- **元素过滤**: 多种过滤条件（类型、可见性、可用性等）
- **批量处理**: 支持批量抓取多个网页
- **错误处理**: 完善的异常处理机制

## 安装依赖

```bash
pip install -r requirements_web_extractor.txt
```

主要依赖：
- `selenium>=4.15.0` - WebDriver自动化
- `webdriver-manager>=4.0.0` - 自动管理ChromeDriver

## 快速开始

### 基本使用

```python
from web_element_extractor import WebElementExtractor

# 创建抓取工具实例
extractor = WebElementExtractor(headless=True, timeout=10)

# 抓取网页元素
url = "https://www.example.com"
elements = extractor.extract_elements(url)

# 打印元素摘要
extractor.print_elements_summary(elements)

# 保存到JSON文件
extractor.save_to_json(elements, "elements.json")
```

### 命令行使用

```bash
python web_element_extractor.py
# 然后输入要抓取的URL
```

## 元素数据结构

每个抓取的元素包含以下信息：

```python
@dataclass
class WebElement:
    element_type: str          # 元素类型：button, input, select, link等
    element_name: str          # 元素名称
    xpath: str                 # 元素XPath
    css_selector: str          # CSS选择器
    element_id: str           # 元素ID
    element_class: str        # 元素class
    tag_name: str             # 标签名
    text_content: str         # 文本内容
    placeholder: str          # placeholder属性
    value: str                # value属性
    href: str                 # href属性（链接）
    is_visible: bool          # 是否可见
    is_enabled: bool          # 是否可用
    position: Dict[str, int]  # 元素位置
    size: Dict[str, int]      # 元素大小
```

## 支持的元素类型

### 🔘 按钮 (button)
- `<button>`
- `<input type="button|submit|reset">`
- `[role="button"]`
- `.btn`, `.button` 类

### 📝 输入框 (input)
- `<input type="text|password|email|number|tel|url|search|date|time|...">`
- `<textarea>`

### ☑️ 选择框
- **复选框 (checkbox)**: `<input type="checkbox">`
- **单选框 (radio)**: `<input type="radio">`
- **下拉选择 (select)**: `<select>`

### 🔗 链接 (link)
- `<a href="...">`

### 🖼️ 图片 (image)
- `<img onclick="...">` 或 `<img role="button">`

## 高级用法

### 元素过滤

```python
# 只获取按钮元素
buttons = extractor.filter_elements(elements, element_type='button')

# 只获取可见且可用的元素
visible_enabled = extractor.filter_elements(
    elements, 
    visible_only=True, 
    enabled_only=True
)

# 只获取有文本内容的元素
with_text = extractor.filter_elements(elements, has_text=True)
```

### 按类型分组

```python
# 按元素类型分组
grouped = extractor.get_elements_by_type(elements)

for element_type, type_elements in grouped.items():
    print(f"{element_type}: {len(type_elements)} 个")
```

### 生成Robot Framework代码

```python
# 生成RF测试脚本
rf_code = extractor.generate_robot_framework_keywords(elements)

# 保存到文件
with open("test_script.robot", "w", encoding="utf-8") as f:
    f.write(rf_code)
```

### 自定义选择器

```python
# 添加自定义元素选择器
extractor.interactive_selectors['custom'] = [
    '[data-testid]',
    '[data-cy]',
    '[data-test]'
]
```

## 配置选项

### WebElementExtractor 参数

```python
extractor = WebElementExtractor(
    headless=True,    # 是否使用无头模式
    timeout=10        # 页面加载超时时间（秒）
)
```

### Chrome选项

工具自动配置以下Chrome选项：
- `--no-sandbox`
- `--disable-dev-shm-usage`
- `--disable-gpu`
- `--window-size=1920,1080`
- 自定义User-Agent

## 输出示例

### 控制台输出
```
找到 15 个可交互元素:
================================================================================
元素类型统计:
  button: 3 个
  input: 5 个
  select: 2 个
  link: 4 个
  checkbox: 1 个

详细元素列表:
--------------------------------------------------------------------------------
1. 类型: input
   名称: 搜索
   XPath: //input[@id='kw']
   CSS选择器: #kw
   ID: kw

2. 类型: button
   名称: 百度一下
   XPath: //input[@id='su']
   CSS选择器: #su
   ID: su
```

### JSON输出
```json
[
  {
    "element_type": "input",
    "element_name": "搜索",
    "xpath": "//input[@id='kw']",
    "css_selector": "#kw",
    "element_id": "kw",
    "element_class": "s_ipt",
    "tag_name": "input",
    "text_content": null,
    "placeholder": "请输入搜索内容",
    "value": "",
    "href": null,
    "is_visible": true,
    "is_enabled": true,
    "position": {"x": 100, "y": 200},
    "size": {"width": 300, "height": 40}
  }
]
```

### Robot Framework输出
```robot
*** Variables ***
# 页面元素定位器
${INPUT_1}    //input[@id='kw']
${BUTTON_1}   //input[@id='su']

*** Test Cases ***
页面元素交互测试
    [Documentation]    测试页面元素交互功能
    [Tags]    ui    interaction

    # 输入到搜索
    网页端 等待并输入文本    ${INPUT_1}    测试文本

    # 点击百度一下
    网页端 等待并点击元素    ${BUTTON_1}
```

## 测试

运行测试套件：

```bash
python test_web_element_extractor.py
```

测试包括：
- 基本元素抓取
- 元素过滤功能
- Robot Framework代码生成
- JSON导出功能
- 自定义选择器

## 注意事项

1. **Chrome浏览器**: 需要安装Chrome浏览器，工具会自动下载ChromeDriver
2. **网络连接**: 需要网络连接来访问目标网页和下载ChromeDriver
3. **页面加载**: 某些动态页面可能需要调整timeout参数
4. **元素可见性**: 只抓取可见且可用的元素
5. **XPath稳定性**: 生成的XPath可能因页面结构变化而失效

## 常见问题

### Q: ChromeDriver下载失败？
A: 检查网络连接，或手动下载ChromeDriver并设置PATH

### Q: 抓取不到动态加载的元素？
A: 增加timeout参数，或在抓取前添加等待时间

### Q: XPath定位不准确？
A: 工具会生成多种定位策略，建议结合ID、class等属性使用

### Q: 内存占用过高？
A: 使用headless模式，及时关闭浏览器实例

## 扩展开发

### 添加新的元素类型

```python
# 在interactive_selectors中添加新类型
extractor.interactive_selectors['new_type'] = [
    'selector1',
    'selector2'
]
```

### 自定义元素名称生成

```python
# 重写_get_element_name方法
def custom_get_element_name(self, element, element_type):
    # 自定义命名逻辑
    return custom_name
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
