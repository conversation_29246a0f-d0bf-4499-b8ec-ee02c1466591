<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px" class="sidebar">
        <div class="logo">
          <h2>UI Automation</h2>
        </div>

        <el-menu
          :default-active="$route.path"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <el-submenu index="web">
            <template slot="title">
              <i class="el-icon-monitor"></i>
              <span>Web 测试</span>
            </template>

            <el-menu-item index="/web/robot-framework-create">
              <i class="el-icon-document-add"></i>
              <span>Robot Framework</span>
            </el-menu-item>
          </el-submenu>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'Layout'
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;

  .el-container {
    height: 100%;
  }

  .sidebar {
    background-color: #304156;

    .logo {
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid #434a50;

      h2 {
        color: #fff;
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .sidebar-menu {
      border: none;

      ::v-deep .el-submenu__title {
        height: 50px;
        line-height: 50px;
        padding-left: 20px !important;

        i {
          margin-right: 8px;
        }
      }

      ::v-deep .el-menu-item {
        height: 45px;
        line-height: 45px;
        padding-left: 45px !important;

        i {
          margin-right: 8px;
        }

        &:hover {
          background-color: #434a50 !important;
        }

        &.is-active {
          background-color: #409EFF !important;
        }
      }
    }
  }

  .main-content {
    background-color: #f5f5f5;
    padding: 0;

    .content-wrapper {
      padding: 20px;
      height: 100%;
      overflow-y: auto;
    }
  }
}
</style>
