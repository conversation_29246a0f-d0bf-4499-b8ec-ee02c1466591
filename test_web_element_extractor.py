#!/usr/bin/env python3
"""
网页元素抓取工具测试脚本
演示如何使用WebElementExtractor类抓取网页元素
"""

import json
from web_element_extractor import WebElementExtractor, WebElement

def test_basic_extraction():
    """测试基本的元素抓取功能"""
    print("🧪 测试基本元素抓取功能")
    print("=" * 50)
    
    # 创建抓取工具实例
    extractor = WebElementExtractor(headless=True, timeout=10)
    
    # 测试URL列表
    test_urls = [
        "https://www.baidu.com",
        "https://httpbin.org/forms/post",  # 包含各种表单元素的测试页面
    ]
    
    for url in test_urls:
        print(f"\n🔍 测试URL: {url}")
        print("-" * 40)
        
        try:
            # 抓取元素
            elements = extractor.extract_elements(url)
            
            if elements:
                print(f"✅ 成功抓取 {len(elements)} 个元素")
                
                # 按类型统计
                type_counts = {}
                for element in elements:
                    type_counts[element.element_type] = type_counts.get(element.element_type, 0) + 1
                
                print("📊 元素类型统计:")
                for element_type, count in type_counts.items():
                    print(f"  - {element_type}: {count} 个")
                
                # 显示前几个元素的详细信息
                print(f"\n📋 前5个元素详情:")
                for i, element in enumerate(elements[:5], 1):
                    print(f"  {i}. {element.element_type}: {element.element_name}")
                    print(f"     XPath: {element.xpath}")
                    print(f"     CSS: {element.css_selector}")
                    if element.element_id:
                        print(f"     ID: {element.element_id}")
                    print()
                
            else:
                print("❌ 未找到任何元素")
                
        except Exception as e:
            print(f"❌ 抓取失败: {e}")

def test_element_filtering():
    """测试元素过滤功能"""
    print("\n🧪 测试元素过滤功能")
    print("=" * 50)
    
    extractor = WebElementExtractor(headless=True, timeout=10)
    
    try:
        # 抓取百度首页元素
        url = "https://www.baidu.com"
        print(f"🔍 抓取并过滤: {url}")
        
        elements = extractor.extract_elements(url)
        
        if not elements:
            print("❌ 未找到任何元素")
            return
        
        print(f"📊 总元素数: {len(elements)}")
        
        # 测试各种过滤条件
        filters = [
            {'element_type': 'button'},
            {'element_type': 'input'},
            {'element_type': 'link'},
            {'visible_only': True},
            {'enabled_only': True},
            {'has_text': True},
            {'has_id': True}
        ]
        
        for filter_condition in filters:
            filtered = extractor.filter_elements(elements, **filter_condition)
            filter_desc = ', '.join([f"{k}={v}" for k, v in filter_condition.items()])
            print(f"🔍 过滤条件 [{filter_desc}]: {len(filtered)} 个元素")
        
        # 按类型分组
        grouped = extractor.get_elements_by_type(elements)
        print(f"\n📊 按类型分组:")
        for element_type, type_elements in grouped.items():
            print(f"  - {element_type}: {len(type_elements)} 个")
        
    except Exception as e:
        print(f"❌ 过滤测试失败: {e}")

def test_robot_framework_generation():
    """测试Robot Framework代码生成"""
    print("\n🧪 测试Robot Framework代码生成")
    print("=" * 50)
    
    extractor = WebElementExtractor(headless=True, timeout=10)
    
    try:
        # 使用一个包含表单的测试页面
        url = "https://httpbin.org/forms/post"
        print(f"🔍 生成RF代码: {url}")
        
        elements = extractor.extract_elements(url)
        
        if not elements:
            print("❌ 未找到任何元素")
            return
        
        print(f"📊 找到 {len(elements)} 个元素")
        
        # 生成Robot Framework代码
        rf_code = extractor.generate_robot_framework_keywords(elements)
        
        # 保存到文件
        filename = "test_generated.robot"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(rf_code)
        
        print(f"✅ Robot Framework代码已生成并保存到: {filename}")
        
        # 显示部分代码
        lines = rf_code.split('\n')
        print(f"\n📝 生成的代码预览 (前20行):")
        print("-" * 40)
        for i, line in enumerate(lines[:20], 1):
            print(f"{i:2d}: {line}")
        
        if len(lines) > 20:
            print(f"... 还有 {len(lines) - 20} 行")
        
    except Exception as e:
        print(f"❌ RF代码生成失败: {e}")

def test_json_export():
    """测试JSON导出功能"""
    print("\n🧪 测试JSON导出功能")
    print("=" * 50)
    
    extractor = WebElementExtractor(headless=True, timeout=10)
    
    try:
        url = "https://www.baidu.com"
        print(f"🔍 导出JSON: {url}")
        
        elements = extractor.extract_elements(url)
        
        if not elements:
            print("❌ 未找到任何元素")
            return
        
        # 导出到JSON
        filename = "test_elements.json"
        extractor.save_to_json(elements, filename)
        
        # 验证JSON文件
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ JSON文件已生成: {filename}")
        print(f"📊 包含 {len(data)} 个元素")
        
        # 显示第一个元素的结构
        if data:
            print(f"\n📋 第一个元素的JSON结构:")
            print("-" * 40)
            first_element = data[0]
            for key, value in first_element.items():
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"❌ JSON导出失败: {e}")

def test_custom_selectors():
    """测试自定义选择器"""
    print("\n🧪 测试自定义选择器扩展")
    print("=" * 50)
    
    extractor = WebElementExtractor(headless=True, timeout=10)
    
    # 添加自定义选择器
    extractor.interactive_selectors['custom'] = [
        '[data-testid]',
        '[data-cy]',
        '[data-test]'
    ]
    
    print("✅ 已添加自定义选择器:")
    print("  - [data-testid]")
    print("  - [data-cy]") 
    print("  - [data-test]")
    
    try:
        url = "https://www.baidu.com"
        elements = extractor.extract_elements(url)
        
        custom_elements = [e for e in elements if e.element_type == 'custom']
        print(f"🔍 找到 {len(custom_elements)} 个自定义元素")
        
    except Exception as e:
        print(f"❌ 自定义选择器测试失败: {e}")

def main():
    """主测试函数"""
    print("🕷️ 网页元素抓取工具测试套件")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        test_basic_extraction,
        test_element_filtering,
        test_robot_framework_generation,
        test_json_export,
        test_custom_selectors
    ]
    
    for i, test_func in enumerate(tests, 1):
        try:
            print(f"\n🧪 运行测试 {i}/{len(tests)}: {test_func.__name__}")
            test_func()
            print(f"✅ 测试 {test_func.__name__} 完成")
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🏁 所有测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
