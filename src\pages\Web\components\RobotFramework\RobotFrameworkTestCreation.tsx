import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  Upload,
  Input,
  Button,
  Form,
  Space,
  Typography,
  Alert,
  Progress,
  Divider,
  Tag,
  Row,
  Col,
  message,
  Modal,
  Spin
} from 'antd';
import {
  UploadOutlined,
  LinkOutlined,
  PlayCircleOutlined,
  DownloadOutlined,
  EyeOutlined,
  SaveOutlined,
  CodeOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';

import StreamingDisplay from '../../../../components/StreamingDisplay/StreamingDisplay';
import {
  analyzeRobotFramework,
  getRobotFrameworkScripts,
  downloadRobotFrameworkScript,
  saveScriptFromSession
} from '../../../../services/api';
import './RobotFrameworkTestCreation.css';

const { TextArea } = Input;
const { Title, Text } = Typography;

interface AnalysisResult {
  session_id: string;
  analysis_result: any;
  robot_script: any;
  robot_content: string;
  file_path: string;
  estimated_duration?: string;
  generated_scripts?: Array<{
    format: string;
    content: string;
    file_path: string;
  }>;
}

interface RobotFrameworkTestCreationProps {}

const RobotFrameworkTestCreation: React.FC<RobotFrameworkTestCreationProps> = () => {
  // 基础状态
  const [form] = Form.useForm();
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const [preserveStreamingContent, setPreserveStreamingContent] = useState<boolean>(false);
  const [scripts, setScripts] = useState<{[key: string]: any}>({});
  const [isSavingScript, setIsSavingScript] = useState(false);
  const [isExecutingScript, setIsExecutingScript] = useState(false);

  // 处理文件上传
  const handleImageUpload = useCallback((file: any) => {
    // 验证文件类型
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件！');
      return false;
    }

    // 验证文件大小（5MB限制）
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过5MB！');
      return false;
    }

    setUploadedFile(file);
    message.success(`${file.name} 文件上传成功`);
    return false; // 阻止自动上传
  }, []);

  // 处理Robot Framework分析
  const handleRobotFrameworkAnalysis = useCallback(async (values: any) => {
    if (!uploadedFile && !values.page_url) {
      message.error('请上传图片或输入页面URL');
      return;
    }

    setIsAnalyzing(true);

    const formData = new FormData();
    
    // 添加文件（如果有）
    if (uploadedFile) {
      formData.append('file', uploadedFile);
    }

    // 添加基本参数
    formData.append('analysis_type', 'comprehensive');
    formData.append('script_name', values.script_name || `Robot Framework测试_${Date.now()}`);
    
    // 添加页面信息
    if (values.page_url) {
      formData.append('page_url', values.page_url);
    }
    
    // 添加交互描述
    if (values.interaction_description) {
      formData.append('interaction_description', values.interaction_description);
    }
    
    // 添加附加上下文
    if (values.additional_context) {
      formData.append('additional_context', values.additional_context);
    }

    // 数据库保存配置
    formData.append('save_to_database', 'true');
    formData.append('script_description', values.interaction_description || 'Robot Framework自动化测试脚本');
    formData.append('tags', JSON.stringify(['robot_framework', 'web_test']));
    formData.append('category', 'robot_framework');
    formData.append('priority', '1');

    try {
      const result = await analyzeRobotFramework(formData);

      if (result.sse_endpoint && result.session_id) {
        setCurrentSessionId(result.session_id);
        toast.success('开始实时分析...');
      } else {
        setAnalysisResult(result);
        setIsAnalyzing(false);
        if (result.session_id) {
          setCurrentSessionId(result.session_id);
          await fetchGeneratedScripts(result.session_id);
        }
        toast.success('Robot Framework分析完成！');
      }
    } catch (error: any) {
      setIsAnalyzing(false);
      toast.error(`分析失败: ${error.message}`);
    }
  }, [uploadedFile]);

  // 获取生成的脚本
  const fetchGeneratedScripts = useCallback(async (sessionId: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const data = await getRobotFrameworkScripts(sessionId);

      if (data.status === 'success' && data.scripts) {
        const scriptsMap: {[key: string]: any} = {};

        data.scripts.forEach((script: any) => {
          if (script.format === 'robot') {
            scriptsMap['robot'] = script;
          }
        });

        setScripts(scriptsMap);
        toast.success(`获取到 ${data.scripts.length} 个生成的脚本文件`);
      }
    } catch (error: any) {
      console.error('获取脚本失败:', error);
      toast.error('获取脚本失败');
    }
  }, []);

  // 处理流式分析完成
  const handleStreamingComplete = useCallback(async (result: any) => {
    setAnalysisResult(result);
    setIsAnalyzing(false);
    setPreserveStreamingContent(true); // 保持流式内容显示

    if (result && result.session_id) {
      setCurrentSessionId(result.session_id);
      await fetchGeneratedScripts(result.session_id);
    }

    toast.success('分析完成！');
  }, [fetchGeneratedScripts]);

  // 保存脚本到数据库
  const handleSaveScript = useCallback(async () => {
    const script = scripts['robot'];
    if (!script || !currentSessionId) {
      message.error('没有可保存的脚本或会话ID');
      return;
    }

    setIsSavingScript(true);

    try {
      const saveData = {
        session_id: currentSessionId,
        name: `Robot Framework测试脚本_${Date.now()}`,
        description: '自动生成的Robot Framework测试脚本',
        script_format: 'robot',
        script_type: 'robot_framework_analysis',
        test_description: form.getFieldValue('interaction_description') || '',
        content: script.content,
        tags: ['robot_framework', 'web_test']
      };

      const result = await saveScriptFromSession(saveData);

      if (result.status === 'success') {
        toast.success('脚本保存成功！');
        message.success(`脚本已保存到数据库，ID: ${result.script_id}`);
      }
    } catch (error: any) {
      toast.error(`保存失败: ${error.message}`);
    } finally {
      setIsSavingScript(false);
    }
  }, [scripts, currentSessionId, form]);

  // 下载脚本
  const handleDownloadScript = useCallback(async () => {
    if (!currentSessionId) {
      message.error('没有可下载的脚本');
      return;
    }

    try {
      const blob = await downloadRobotFrameworkScript(currentSessionId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `robot_framework_test_${currentSessionId}.robot`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('脚本下载成功！');
    } catch (error: any) {
      toast.error(`下载失败: ${error.message}`);
    }
  }, [currentSessionId]);

  return (
    <div className="robot-framework-test-creation">
      <Card title="Robot Framework 测试创建" className="main-card">
        <Form form={form} onFinish={handleRobotFrameworkAnalysis} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="上传页面截图" tooltip="上传页面截图用于元素分析">
                <Upload
                  beforeUpload={handleImageUpload}
                  showUploadList={false}
                  accept="image/*"
                >
                  <Button icon={<UploadOutlined />}>
                    {uploadedFile ? uploadedFile.name : '选择图片'}
                  </Button>
                </Upload>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item 
                name="page_url" 
                label="页面URL" 
                tooltip="页面URL用于自动抓取页面元素"
              >
                <Input placeholder="https://example.com" prefix={<LinkOutlined />} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="interaction_description"
            label="测试描述"
            rules={[{ required: true, message: '请输入测试描述' }]}
            tooltip="描述要测试的功能和操作步骤"
          >
            <TextArea rows={4} placeholder="描述要测试的功能和操作步骤，例如：登录功能测试，输入用户名密码，点击登录按钮，验证登录成功..." />
          </Form.Item>

          <Form.Item 
            name="additional_context" 
            label="附加上下文"
            tooltip="提供额外的测试上下文信息"
          >
            <TextArea rows={3} placeholder="提供额外的测试上下文，如特殊的验证规则、数据准备要求等..." />
          </Form.Item>

          <Form.Item name="script_name" label="脚本名称">
            <Input placeholder="Robot Framework测试脚本名称（可选）" />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isAnalyzing}
              icon={<EyeOutlined />}
              size="large"
            >
              {isAnalyzing ? '分析中...' : '开始分析'}
            </Button>
          </Form.Item>
        </Form>

        {/* 分析进度和结果显示区域 */}
        {(isAnalyzing || analysisResult || currentSessionId) && (
          <Divider orientation="left">分析结果</Divider>
        )}

        {/* 流式显示组件 */}
        {currentSessionId && (
          <StreamingDisplay
            sessionId={currentSessionId}
            endpoint={`/api/v1/web/robot-framework/stream/${currentSessionId}`}
            onComplete={handleStreamingComplete}
            preserveContent={preserveStreamingContent}
          />
        )}

        {/* 脚本显示和操作区域 */}
        {scripts.robot && (
          <Card title="生成的Robot Framework脚本" style={{ marginTop: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Space>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSaveScript}
                  loading={isSavingScript}
                >
                  保存到数据库
                </Button>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleDownloadScript}
                >
                  下载脚本
                </Button>
              </Space>
              
              <div style={{ 
                background: '#f5f5f5', 
                padding: '16px', 
                borderRadius: '6px',
                maxHeight: '400px',
                overflow: 'auto'
              }}>
                <pre style={{ margin: 0, fontSize: '12px', lineHeight: '1.4' }}>
                  {scripts.robot.content}
                </pre>
              </div>
            </Space>
          </Card>
        )}
      </Card>
    </div>
  );
};

export default RobotFrameworkTestCreation;
