import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  Upload,
  Input,
  Button,
  Form,
  Divider,
  message
} from 'antd';
import {
  UploadOutlined,
  LinkOutlined,
  EyeOutlined
} from '@ant-design/icons';
import toast from 'react-hot-toast';

import StreamingDisplay from '../../../../components/StreamingDisplay/StreamingDisplay';
import {
  analyzeRobotFramework
} from '../../../../services/api';
import './RobotFrameworkTestCreation.css';

const { TextArea } = Input;

interface AnalysisResult {
  session_id: string;
  analysis_result?: any;
  robot_script?: any;
  robot_content?: string;
  file_path?: string;
  estimated_duration?: string;
  sse_endpoint?: string;
  status?: string;
  message?: string;
  generated_scripts?: Array<{
    format: string;
    content: string;
    file_path: string;
  }>;
}

interface RobotFrameworkTestCreationProps {}

const RobotFrameworkTestCreation: React.FC<RobotFrameworkTestCreationProps> = () => {
  // 基础状态
  const [form] = Form.useForm();
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<any>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const [preserveStreamingContent, setPreserveStreamingContent] = useState<boolean>(false);

  // 处理文件上传
  const handleImageUpload = useCallback((file: any) => {
    // 验证文件类型
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件！');
      return false;
    }

    // 验证文件大小（5MB限制）
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过5MB！');
      return false;
    }

    setUploadedFile(file);
    message.success(`${file.name} 文件上传成功`);
    return false; // 阻止自动上传
  }, []);

  // 处理Robot Framework分析
  const handleRobotFrameworkAnalysis = useCallback(async (values: any) => {
    if (!values.page_url) {
      message.error('请输入页面URL');
      return;
    }

    setIsAnalyzing(true);

    const formData = new FormData();
    
    // 添加文件（如果有）
    if (uploadedFile) {
      formData.append('file', uploadedFile);
    }

    // 添加基本参数
    formData.append('analysis_type', 'comprehensive');

    // 添加页面信息
    if (values.page_url) {
      formData.append('page_url', values.page_url);
    }
    
    // 添加交互描述
    if (values.interaction_description) {
      formData.append('interaction_description', values.interaction_description);
    }
    
    // 添加附加上下文
    if (values.additional_context) {
      formData.append('additional_context', values.additional_context);
    }

    // 数据库保存配置
    formData.append('save_to_database', 'true');
    formData.append('script_description', values.interaction_description || 'Robot Framework自动化测试脚本');
    formData.append('tags', JSON.stringify(['robot_framework', 'web_test']));
    formData.append('category', 'robot_framework');
    formData.append('priority', '1');

    try {
      const result = await analyzeRobotFramework(formData);

      if ((result as any).sse_endpoint && result.session_id) {
        setCurrentSessionId(result.session_id);
        toast.success('开始实时分析...');
      } else {
        setAnalysisResult(result);
        setIsAnalyzing(false);
        if (result.session_id) {
          setCurrentSessionId(result.session_id);
        }
        toast.success('Robot Framework分析完成！');
      }
    } catch (error: any) {
      setIsAnalyzing(false);
      toast.error(`分析失败: ${error.message}`);
    }
  }, [uploadedFile]);



  // 处理流式分析完成
  const handleStreamingComplete = useCallback(async (result: any) => {
    setAnalysisResult(result);
    setIsAnalyzing(false);
    setPreserveStreamingContent(true); // 保持流式内容显示

    if (result && result.session_id) {
      setCurrentSessionId(result.session_id);
    }

    toast.success('Robot Framework分析完成！');
  }, []);



  return (
    <div className="robot-framework-test-creation">
      <Card title="Robot Framework 测试创建" className="main-card">
        <Form form={form} onFinish={handleRobotFrameworkAnalysis} layout="vertical">
          <Form.Item
            name="page_url"
            label="页面URL"
            rules={[
              { required: true, message: '请输入页面URL' },
              { type: 'url', message: '请输入有效的URL' }
            ]}
            tooltip="页面URL用于自动抓取页面元素"
          >
            <Input placeholder="https://example.com" prefix={<LinkOutlined />} />
          </Form.Item>

          <Form.Item label="上传页面截图（可选）" tooltip="上传页面截图用于辅助元素分析">
            <Upload
              beforeUpload={handleImageUpload}
              showUploadList={false}
              accept="image/*"
            >
              <Button icon={<UploadOutlined />}>
                {uploadedFile ? uploadedFile.name : '选择图片'}
              </Button>
            </Upload>
          </Form.Item>

          <Form.Item
            name="interaction_description"
            label="测试描述（可选）"
            tooltip="描述要测试的功能和操作步骤"
          >
            <TextArea rows={4} placeholder="描述要测试的功能和操作步骤，例如：登录功能测试，输入用户名密码，点击登录按钮，验证登录成功..." />
          </Form.Item>

          <Form.Item
            name="additional_context"
            label="附加上下文"
            tooltip="提供额外的测试上下文信息"
          >
            <TextArea rows={3} placeholder="提供额外的测试上下文，如特殊的验证规则、数据准备要求等..." />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isAnalyzing}
              icon={<EyeOutlined />}
              size="large"
            >
              {isAnalyzing ? '分析中...' : '开始分析'}
            </Button>
          </Form.Item>
        </Form>

        {/* 分析进度和结果显示区域 */}
        {(isAnalyzing || analysisResult || currentSessionId) && (
          <Divider orientation="left">分析结果</Divider>
        )}

        {/* 流式显示组件 */}
        {currentSessionId && (
          <StreamingDisplay
            sessionId={currentSessionId}
            isActive={isAnalyzing || preserveStreamingContent}
            onAnalysisComplete={handleStreamingComplete}
            onError={(error) => {
              console.error('分析错误:', error);
              toast.error(`分析错误: ${error}`);
              setIsAnalyzing(false);
            }}
            streamEndpoint={`/api/v1/web/robot-framework/stream/${currentSessionId}`}
          />
        )}

        {/* 分析结果显示区域 */}
        {analysisResult && (
          <Card title="分析结果" style={{ marginTop: 16 }}>
            <Form.Item label="Robot Framework脚本">
              <TextArea
                value={analysisResult.robot_content || (analysisResult as any).content || JSON.stringify(analysisResult, null, 2)}
                rows={15}
                readOnly
                style={{
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                  fontSize: '12px',
                  backgroundColor: '#f5f5f5'
                }}
              />
            </Form.Item>
          </Card>
        )}
      </Card>
    </div>
  );
};

export default RobotFrameworkTestCreation;
