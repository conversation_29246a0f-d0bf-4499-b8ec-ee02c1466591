<template>
  <div class="streaming-display">
    <el-card v-if="isActive || messages.length > 0" class="streaming-card">
      <div slot="header" class="card-header">
        <span>实时分析结果</span>
        <el-button 
          v-if="!isActive && messages.length > 0" 
          type="text" 
          size="small" 
          @click="clearMessages"
        >
          清空
        </el-button>
      </div>
      
      <div class="streaming-content" ref="streamingContent">
        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          class="message-item"
          :class="getMessageClass(message)"
        >
          <div class="message-content" v-html="formatMessage(message.content)"></div>
          <div v-if="message.timestamp" class="message-timestamp">
            {{ formatTimestamp(message.timestamp) }}
          </div>
        </div>
        
        <div v-if="isActive" class="loading-indicator">
          <i class="el-icon-loading"></i>
          <span>分析中...</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'StreamingDisplay',
  
  props: {
    sessionId: {
      type: String,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    },
    streamEndpoint: {
      type: String,
      default: null
    }
  },
  
  data() {
    return {
      messages: [],
      eventSource: null,
      reconnectAttempts: 0,
      maxReconnectAttempts: 3
    }
  },
  
  watch: {
    sessionId: {
      handler(newSessionId) {
        if (newSessionId && this.isActive) {
          this.startStreaming()
        }
      },
      immediate: true
    },
    
    isActive(newValue) {
      if (newValue && this.sessionId) {
        this.startStreaming()
      } else {
        this.stopStreaming()
      }
    }
  },
  
  beforeDestroy() {
    this.stopStreaming()
  },
  
  methods: {
    startStreaming() {
      this.stopStreaming()
      
      const baseUrl = process.env.VUE_APP_API_BASE_URL || '/api'
      const endpoint = this.streamEndpoint || `/v1/web/create/stream/${this.sessionId}`
      const sseUrl = `${baseUrl}${endpoint}`
      
      try {
        this.eventSource = new EventSource(sseUrl)
        
        this.eventSource.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
          } catch (error) {
            console.error('解析消息失败:', error)
          }
        }
        
        this.eventSource.onerror = (error) => {
          console.error('SSE连接错误:', error)
          this.handleConnectionError()
        }
        
        this.eventSource.onopen = () => {
          console.log('SSE连接已建立')
          this.reconnectAttempts = 0
        }
        
      } catch (error) {
        console.error('创建SSE连接失败:', error)
        this.$emit('error', '无法建立实时连接')
      }
    },
    
    stopStreaming() {
      if (this.eventSource) {
        this.eventSource.close()
        this.eventSource = null
      }
    },
    
    handleMessage(data) {
      const message = {
        content: data.content || data.message || '',
        type: data.type || 'info',
        timestamp: new Date(),
        data: data
      }
      
      this.messages.push(message)
      this.$nextTick(() => {
        this.scrollToBottom()
      })
      
      // 检查是否为最终消息
      if (data.is_final) {
        this.$emit('final-complete', data)
        this.$emit('analysis-complete', data)
      }
      
      // 检查是否完成
      if (data.type === 'complete' || data.status === 'completed') {
        this.$emit('analysis-complete', data)
      }
    },
    
    handleConnectionError() {
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++
        setTimeout(() => {
          if (this.isActive) {
            this.startStreaming()
          }
        }, 2000 * this.reconnectAttempts)
      } else {
        this.$emit('error', '连接失败，请刷新页面重试')
      }
    },
    
    clearMessages() {
      this.messages = []
    },
    
    scrollToBottom() {
      const container = this.$refs.streamingContent
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },
    
    getMessageClass(message) {
      return {
        'message-error': message.type === 'error',
        'message-warning': message.type === 'warning',
        'message-success': message.type === 'success',
        'message-info': message.type === 'info'
      }
    },
    
    formatMessage(content) {
      if (!content) return ''
      
      // 简单的 markdown 格式化
      return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>')
    },
    
    formatTimestamp(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    }
  }
}
</script>

<style lang="scss" scoped>
.streaming-display {
  margin-top: 16px;
  
  .streaming-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .streaming-content {
      max-height: 400px;
      overflow-y: auto;
      
      .message-item {
        margin-bottom: 12px;
        padding: 8px 12px;
        border-radius: 4px;
        background-color: #f8f9fa;
        
        &.message-error {
          background-color: #fef0f0;
          border-left: 4px solid #f56c6c;
        }
        
        &.message-warning {
          background-color: #fdf6ec;
          border-left: 4px solid #e6a23c;
        }
        
        &.message-success {
          background-color: #f0f9ff;
          border-left: 4px solid #67c23a;
        }
        
        &.message-info {
          background-color: #f4f4f5;
          border-left: 4px solid #909399;
        }
        
        .message-content {
          font-size: 14px;
          line-height: 1.5;
          word-break: break-word;
          
          ::v-deep code {
            background-color: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 2px;
            font-family: 'Courier New', monospace;
          }
        }
        
        .message-timestamp {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
        }
      }
      
      .loading-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px;
        color: #409eff;
        
        i {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
