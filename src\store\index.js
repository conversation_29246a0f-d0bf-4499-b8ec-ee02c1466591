import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    // 全局加载状态
    loading: false,

    // 用户信息
    user: null,

    // 当前分析会话
    currentSession: null
  },

  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading
    },

    SET_USER(state, user) {
      state.user = user
    },

    SET_CURRENT_SESSION(state, session) {
      state.currentSession = session
    }
  },

  actions: {
    setLoading({ commit }, loading) {
      commit('SET_LOADING', loading)
    },

    setUser({ commit }, user) {
      commit('SET_USER', user)
    },

    setCurrentSession({ commit }, session) {
      commit('SET_CURRENT_SESSION', session)
    }
  },

  getters: {
    isLoading: state => state.loading,
    currentUser: state => state.user,
    currentSession: state => state.currentSession
  }
})
