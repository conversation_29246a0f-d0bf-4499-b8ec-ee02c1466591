2025-07-03 11:03:06 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:342 | 团队分析执行失败: APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1584, in request
    raise self._make_status_error_from_response(err.response) from None

openai.APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}

2025-07-03 11:11:43 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:342 | 团队分析执行失败: APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1584, in request
    raise self._make_status_error_from_response(err.response) from None

openai.APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}

2025-07-03 11:22:44 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:342 | 团队分析执行失败: APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\futures.py", line 284, in __await__
    yield self  # This tells Task to wait for completion.

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 304, in __wakeup
    future.result()

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\futures.py", line 201, in result
    raise self._exception

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 232, in __step
    result = coro.send(None)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1584, in request
    raise self._make_status_error_from_response(err.response) from None

openai.APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}

