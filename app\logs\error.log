2025-07-03 11:03:06 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:342 | 团队分析执行失败: APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1584, in request
    raise self._make_status_error_from_response(err.response) from None

openai.APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}

2025-07-03 11:11:43 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:342 | 团队分析执行失败: APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1584, in request
    raise self._make_status_error_from_response(err.response) from None

openai.APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}

2025-07-03 11:22:44 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:342 | 团队分析执行失败: APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\futures.py", line 284, in __await__
    yield self  # This tells Task to wait for completion.

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 304, in __wakeup
    future.result()

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\futures.py", line 201, in result
    raise self._exception

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 232, in __step
    result = coro.send(None)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1584, in request
    raise self._make_status_error_from_response(err.response) from None

openai.APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}

2025-07-03 16:04:16 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_create_rf_analysis_team:178 | 创建Robot Framework分析团队失败: Graph must have at least one start node
2025-07-03 16:04:16 | ERROR    | app.core.agents.base:handle_exception:126 | [Robot Framework分析智能体] 在handle_message中发生错误: Graph must have at least one start node
2025-07-03 16:04:16 | ERROR    | app.core.agents.base:send_error:109 | [Robot Framework分析智能体] 错误: 在handle_message中发生错误: Graph must have at least one start node
2025-07-03 16:25:08 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_fetch_page_content:293 | 抓取页面内容失败: HTTPSConnectionPool(host='www.baidu.com', port=443): Max retries exceeded with url: / (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F06A8D2620>: Failed to resolve 'www.baidu.com' ([Errno 11001] getaddrinfo failed)"))
2025-07-03 16:25:10 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:349 | 团队分析执行失败: APIConnectionError: Connection error.
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc

httpcore.ConnectError: [Errno 11001] getaddrinfo failed


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1519, in request
    response = await self._client.send(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc

httpx.ConnectError: [Errno 11001] getaddrinfo failed


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1551, in request
    raise APIConnectionError(request=request) from err

openai.APIConnectionError: Connection error.

