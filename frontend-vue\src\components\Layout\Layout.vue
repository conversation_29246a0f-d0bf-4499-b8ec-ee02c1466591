<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px" class="sidebar">
        <div class="logo">
          <h2>UI Automation</h2>
        </div>
        
        <el-menu
          :default-active="$route.path"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <el-submenu index="/web">
            <template slot="title">
              <i class="el-icon-monitor"></i>
              <span>Web 测试</span>
            </template>
            <el-menu-item index="/web/robot-framework-create">
              <i class="el-icon-document-add"></i>
              <span>Robot Framework</span>
            </el-menu-item>
          </el-submenu>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-main class="main-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'Layout'
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  
  .el-container {
    height: 100%;
  }
  
  .sidebar {
    background-color: #304156;
    
    .logo {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #2b3a4b;
      
      h2 {
        color: #fff;
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }
    
    .sidebar-menu {
      border: none;
      height: calc(100vh - 60px);
      overflow-y: auto;
      
      .el-submenu__title {
        height: 50px;
        line-height: 50px;
        
        i {
          margin-right: 8px;
        }
      }
      
      .el-menu-item {
        height: 45px;
        line-height: 45px;
        
        i {
          margin-right: 8px;
        }
      }
    }
  }
  
  .main-content {
    background-color: #f5f5f5;
    padding: 20px;
    
    .content-wrapper {
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      min-height: calc(100vh - 80px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
