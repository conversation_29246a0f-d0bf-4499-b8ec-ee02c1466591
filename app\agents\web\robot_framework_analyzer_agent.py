"""
Robot Framework分析智能体
基于AutoGen团队协作机制，包含3个专业助手：
1. 页面元素分析助手 - 分析页面元素并生成XPath
2. 交互流程分析助手 - 分析用户交互操作流程
3. Robot Framework脚本生成助手 - 生成完整的Robot Framework测试脚本
"""
import json
import uuid
import re
import base64
import requests
from io import BytesIO
from typing import Dict, List, Any, Optional
from datetime import datetime

from autogen_agentchat.base import TaskResult
from autogen_core import message_handler, type_subscription, MessageContext, TopicId
from autogen_core import Image as AGImage
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.messages import TextMessage, MultiModalMessage, ModelClientStreamingChunkEvent
from autogen_agentchat.teams import GraphFlow, DiGraphBuilder
from PIL import Image
from loguru import logger

from app.core.messages.web import (
    RobotFrameworkAnalysisRequest, RobotFrameworkAnalysisResponse,
    RobotFrameworkAnalysisResult, RobotFrameworkIssue, PageElement, InteractionStep
)
from app.core.agents.base import BaseAgent
from app.core.types import TopicTypes, AgentTypes, AGENT_NAMES, MessageRegion


@type_subscription(topic_type=TopicTypes.ROBOT_FRAMEWORK_ANALYZER.value)
class RobotFrameworkAnalyzerAgent(BaseAgent):
    """Robot Framework分析智能体，基于AutoGen团队协作"""

    def __init__(self, model_client_instance=None, enable_user_feedback: bool = False, collector=None, **kwargs):
        """初始化Robot Framework分析智能体"""
        super().__init__(
            agent_id=AgentTypes.ROBOT_FRAMEWORK_ANALYZER.value,
            agent_name=AGENT_NAMES[AgentTypes.ROBOT_FRAMEWORK_ANALYZER.value],
            model_client_instance=model_client_instance,
            **kwargs
        )

        self.metrics = None
        self.enable_user_feedback = enable_user_feedback
        self._analysis_team = None
        self.collector = collector

        logger.info(f"Robot Framework分析智能体初始化完成，用户反馈: {enable_user_feedback}")

    @classmethod
    def create_page_element_analyzer_agent(cls, **kwargs) -> AssistantAgent:
        """创建页面元素分析助手智能体"""
        from app.agents.factory import agent_factory

        return agent_factory.create_assistant_agent(
            name=AgentTypes.RF_PAGE_ELEMENT_ANALYZER.value,
            system_message=cls._build_page_element_analyzer_prompt(),
            model_client_type="qwenvl",  # 使用视觉模型
            **kwargs
        )

    @classmethod
    def create_interaction_flow_analyzer_agent(cls, **kwargs) -> AssistantAgent:
        """创建交互流程分析助手智能体"""
        from app.agents.factory import agent_factory

        return agent_factory.create_assistant_agent(
            name=AgentTypes.RF_INTERACTION_FLOW_ANALYZER.value,
            system_message=cls._build_interaction_flow_analyzer_prompt(),
            model_client_type="qwenvl",
            **kwargs
        )

    @classmethod
    def create_script_generator_agent(cls, **kwargs) -> AssistantAgent:
        """创建Robot Framework脚本生成助手智能体"""
        from app.agents.factory import agent_factory

        return agent_factory.create_assistant_agent(
            name=AgentTypes.RF_SCRIPT_GENERATOR.value,
            system_message=cls._build_script_generator_prompt(),
            model_client_type="qwenvl",
            **kwargs
        )

    @message_handler
    async def handle_message(self, message: RobotFrameworkAnalysisRequest, ctx: MessageContext) -> None:
        """处理Robot Framework分析请求"""
        try:
            monitor_id = self.start_performance_monitoring()
            analysis_id = str(uuid.uuid4())

            await self.send_response("🚀 开始Robot Framework智能分析...\n")

            # 创建分析团队
            team = await self._create_rf_analysis_team()

            # 准备分析任务
            analysis_task = await self._prepare_analysis_task(message)

            # 运行团队分析
            team_results = await self._run_team_analysis(team, analysis_task)
            self.metrics = self.end_performance_monitoring(monitor_id)
            
            # 整合分析结果
            analysis_result = await self._integrate_analysis_results(team_results, analysis_id, message)

            await self.send_response(
                "✅ Robot Framework分析完成",
                is_final=True,
                result={
                    "analysis_result": analysis_result.model_dump(),
                    "team_collaboration": True,
                    "user_feedback_enabled": self.enable_user_feedback,
                    "metrics": self.metrics
                }
            )

        except Exception as e:
            await self.handle_exception("handle_message", e)

    async def _create_rf_analysis_team(self) -> GraphFlow:
        """创建基于GraphFlow的Robot Framework分析团队"""
        try:
            if self._analysis_team:
                return self._analysis_team

            # 使用工厂创建专业智能体
            page_element_analyzer = self.create_page_element_analyzer_agent()
            interaction_flow_analyzer = self.create_interaction_flow_analyzer_agent()
            script_generator = self.create_script_generator_agent()

            # 用户反馈代理（如果启用）
            participants = [page_element_analyzer, interaction_flow_analyzer, script_generator]
            user_proxy = None
            if self.enable_user_feedback and self.collector and self.collector.user_input:
                user_proxy = UserProxyAgent(
                    name="user_proxy",
                    input_func=self.collector.user_input
                )
                participants.append(user_proxy)

            # 构建GraphFlow工作流 - 顺序执行但支持协作
            builder = DiGraphBuilder()

            # 添加所有节点
            for participant in participants:
                builder.add_node(participant)

            # 构建执行图：顺序执行，确保有明确的起始点
            # 执行顺序：页面元素分析助手 -> 交互流程分析助手 -> 脚本生成助手

            # 1. 页面元素分析助手作为起始点（支持图文结合分析）
            # 2. 页面元素分析助手完成分析后，将结果传递给交互流程分析助手
            builder.add_edge(page_element_analyzer, interaction_flow_analyzer)

            # 3. 交互流程分析助手可以访问页面分析结果，进行协作分析
            # 4. 交互流程分析助手完成后，将结果传递给脚本生成助手
            builder.add_edge(interaction_flow_analyzer, script_generator)

            # 用户反馈（如果启用）
            if user_proxy:
                builder.add_edge(script_generator, user_proxy)

            # 构建图并创建GraphFlow团队
            graph = builder.build()
            team = GraphFlow(
                participants=builder.get_participants(),
                graph=graph
            )

            self._analysis_team = team
            return team

        except Exception as e:
            logger.error(f"创建Robot Framework分析团队失败: {str(e)}")
            raise

    async def _prepare_analysis_task(self, request: RobotFrameworkAnalysisRequest):
        """准备分析任务，支持图文结合分析"""
        try:
            # 处理页面URL抓取
            page_content = ""
            if request.page_url:
                page_content = await self._fetch_page_content(request.page_url)

            # 构建基础文本内容
            text_content = f"""
请分析以下内容并生成Robot Framework测试脚本：

**分析类型**: {request.analysis_type}
**页面URL**: {request.page_url or '未提供'}
**脚本名称**: {request.script_name or '未命名脚本'}
**交互描述**: {request.interaction_description or '未提供'}
**额外上下文**: {request.additional_context or '无'}

**页面内容**:
{page_content}

**现有脚本内容**:
{request.script_content or '无'}

工作流程说明：
1. PageElementAnalyzer将分析页面元素，识别所有可交互元素并生成XPath
   - 如果提供了页面截图，将结合图片和页面内容进行图文结合分析
   - 通过视觉识别验证元素的实际显示状态和位置
   - 生成经过视觉验证的精确XPath定位器
2. InteractionFlowAnalyzer将分析用户描述的交互流程，设计测试步骤
3. ScriptGenerator将根据前两步的结果生成完整的Robot Framework脚本
4. 如果启用用户反馈，用户可以提供额外意见

请开始分析工作。
"""

            # 检查是否有图片数据，如果有则创建多模态消息
            if request.image_data or request.image_url:
                await self.send_response("🖼️ 检测到页面截图，将进行图文结合分析...\n")

                # 转换图片为AGImage对象
                ag_image = await self._convert_image_to_agimage(request)

                # 创建多模态消息
                return MultiModalMessage(
                    content=[text_content, ag_image],
                    source="user"
                )
            else:
                # 仅文本分析
                await self.send_response("📝 进行纯文本分析...\n")
                return TextMessage(content=text_content, source="user")

        except Exception as e:
            logger.error(f"准备分析任务失败: {str(e)}")
            raise

    async def _convert_image_to_agimage(self, request: RobotFrameworkAnalysisRequest) -> AGImage:
        """将图片数据转换为AGImage对象"""
        try:
            pil_image = None

            if request.image_url:
                # 从URL获取图片
                await self.send_response(f"🌐 正在从URL获取图片: {request.image_url}\n")
                response = requests.get(request.image_url, timeout=10)
                response.raise_for_status()
                pil_image = Image.open(BytesIO(response.content))

            elif request.image_data:
                # 处理base64数据
                await self.send_response("🔄 正在处理base64图片数据...\n")
                if request.image_data.startswith('data:image'):
                    # 移除data URI前缀
                    base64_data = request.image_data.split(',')[1]
                else:
                    base64_data = request.image_data

                # 解码base64数据并创建PIL图片
                image_bytes = base64.b64decode(base64_data)
                pil_image = Image.open(BytesIO(image_bytes))
            else:
                raise ValueError("缺少图片数据或URL")

            # 转换为AGImage
            ag_image = AGImage(pil_image)
            logger.info(f"成功转换图片为AGImage，尺寸: {pil_image.size}")
            await self.send_response(f"✅ 图片处理完成，尺寸: {pil_image.size}\n")

            return ag_image

        except Exception as e:
            logger.error(f"转换图片失败: {str(e)}")
            await self.send_response(f"❌ 图片处理失败: {str(e)}\n")
            raise

    async def _fetch_page_content(self, url: str) -> str:
        """抓取页面内容"""
        try:
            await self.send_response(f"🌐 正在抓取页面内容: {url}\n")
            
            # 这里可以集成实际的页面抓取逻辑
            # 例如使用 Selenium 或 Playwright
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                return f"页面标题: {url}\n页面内容长度: {len(response.text)} 字符"
            else:
                return f"无法访问页面: {url} (状态码: {response.status_code})"
                
        except Exception as e:
            logger.error(f"抓取页面内容失败: {str(e)}")
            return f"页面抓取失败: {str(e)}"

    async def _run_team_analysis(self, team: GraphFlow, analysis_task: TextMessage) -> Dict[str, Any]:
        """运行团队协作分析"""
        try:
            # 运行团队分析
            stream = team.run_stream(task=analysis_task)
            messages = []
            async for event in stream:  # type: ignore
                # 流式消息
                if isinstance(event, ModelClientStreamingChunkEvent):
                    await self.send_response(content=event.content, region=MessageRegion.ANALYSIS, source=AGENT_NAMES.get(event.source, event.source))
                    continue

                # 最终的完整结果
                if isinstance(event, TaskResult):
                    messages = event.messages
                    continue
                    
            # 收集分析结果
            analysis_results = {
                "page_element_analysis": [],
                "interaction_flow_analysis": [],
                "script_generation": [],
                "user_feedback": [],
                "chat_history": []
            }

            # 解析团队对话历史
            for message in messages:
                if isinstance(message, TextMessage):
                    source = message.source
                    content = message.content
                else:
                    continue

                analysis_results["chat_history"].append({
                    "source": source,
                    "content": content,
                    "timestamp": datetime.now().isoformat()
                })

                # 根据来源分类结果
                if source == AgentTypes.RF_PAGE_ELEMENT_ANALYZER.value:
                    analysis_results["page_element_analysis"].append(content)
                elif source == AgentTypes.RF_INTERACTION_FLOW_ANALYZER.value:
                    analysis_results["interaction_flow_analysis"].append(content)
                elif source == AgentTypes.RF_SCRIPT_GENERATOR.value:
                    analysis_results["script_generation"].append(content)
                elif source == "user_proxy":
                    analysis_results["user_feedback"].append(content)

            return analysis_results

        except Exception as e:
            logger.error(f"团队分析执行失败: {str(e)}")
            # 返回默认结果
            return {
                "page_element_analysis": ["团队分析失败"],
                "interaction_flow_analysis": ["团队分析失败"],
                "script_generation": ["团队分析失败"],
                "user_feedback": [],
                "chat_history": []
            }

    async def _integrate_analysis_results(self, team_results: Dict[str, Any], analysis_id: str,
                                        request: RobotFrameworkAnalysisRequest) -> RobotFrameworkAnalysisResponse:
        """整合团队分析结果"""
        try:
            await self.send_response("🔧 整合团队分析结果...\n\n")

            # 解析各个助手的分析结果
            page_elements = await self._parse_page_elements(team_results.get("page_element_analysis", []))
            interaction_steps = await self._parse_interaction_steps(team_results.get("interaction_flow_analysis", []))
            generated_script = await self._parse_generated_script(team_results.get("script_generation", []))

            # 提取问题和建议
            issues = await self._extract_issues_from_results(team_results)
            recommendations = await self._extract_recommendations_from_results(team_results)

            # 计算总体评分和置信度
            overall_score = await self._calculate_overall_score(team_results)
            confidence_score = await self._calculate_confidence_score(team_results)

            # 构建分析结果
            analysis_result = RobotFrameworkAnalysisResult(
                page_elements=page_elements,
                element_analysis_summary=f"识别了 {len(page_elements)} 个页面元素",
                interaction_steps=interaction_steps,
                flow_analysis_summary=f"设计了 {len(interaction_steps)} 个交互步骤",
                generated_script=generated_script,
                script_sections=await self._parse_script_sections(generated_script),
                issues=issues,
                recommendations=recommendations,
                overall_score=overall_score,
                confidence_score=confidence_score
            )

            return RobotFrameworkAnalysisResponse(
                session_id=request.session_id,
                analysis_id=analysis_id,
                script_name=request.script_name,
                analysis_result=analysis_result,
                processing_time=self.metrics.get("duration_seconds", 0.0),
                status="success",
                message="Robot Framework分析完成"
            )

        except Exception as e:
            logger.error(f"整合团队分析结果失败: {str(e)}")
            raise

    async def _parse_page_elements(self, element_analysis_results: List[str]) -> List[PageElement]:
        """解析页面元素分析结果"""
        try:
            elements = []

            for result_text in element_analysis_results:
                try:
                    # 尝试提取JSON内容
                    json_match = re.search(r'```json\s*(.*?)\s*```', result_text, re.DOTALL)
                    if json_match:
                        result_data = json.loads(json_match.group(1))
                        if "page_elements" in result_data:
                            for element_data in result_data["page_elements"]:
                                element = PageElement(**element_data)
                                elements.append(element)
                except (json.JSONDecodeError, AttributeError, TypeError):
                    continue

            return elements

        except Exception as e:
            logger.error(f"解析页面元素失败: {str(e)}")
            return []

    async def _parse_interaction_steps(self, flow_analysis_results: List[str]) -> List[InteractionStep]:
        """解析交互流程分析结果"""
        try:
            steps = []

            for result_text in flow_analysis_results:
                try:
                    json_match = re.search(r'```json\s*(.*?)\s*```', result_text, re.DOTALL)
                    if json_match:
                        result_data = json.loads(json_match.group(1))
                        if "interaction_steps" in result_data:
                            for step_data in result_data["interaction_steps"]:
                                step = InteractionStep(**step_data)
                                steps.append(step)
                except (json.JSONDecodeError, AttributeError, TypeError):
                    continue

            return steps

        except Exception as e:
            logger.error(f"解析交互步骤失败: {str(e)}")
            return []

    async def _parse_generated_script(self, script_generation_results: List[str]) -> str:
        """解析生成的Robot Framework脚本"""
        try:
            for result_text in script_generation_results:
                # 查找Robot Framework脚本内容
                robot_match = re.search(r'```robot\s*(.*?)\s*```', result_text, re.DOTALL)
                if robot_match:
                    return robot_match.group(1).strip()

                # 如果没有找到robot代码块，查找其他代码块
                code_match = re.search(r'```\s*(.*?)\s*```', result_text, re.DOTALL)
                if code_match:
                    return code_match.group(1).strip()

            return "# 未能生成Robot Framework脚本"

        except Exception as e:
            logger.error(f"解析生成脚本失败: {str(e)}")
            return "# 脚本解析失败"

    async def _parse_script_sections(self, script_content: str) -> Dict[str, str]:
        """解析脚本各部分内容"""
        try:
            sections = {}
            current_section = None
            current_content = []

            for line in script_content.split('\n'):
                line = line.strip()
                if line.startswith('***') and line.endswith('***'):
                    # 保存前一个section
                    if current_section:
                        sections[current_section] = '\n'.join(current_content)

                    # 开始新的section
                    current_section = line.strip('* ')
                    current_content = []
                elif current_section:
                    current_content.append(line)

            # 保存最后一个section
            if current_section:
                sections[current_section] = '\n'.join(current_content)

            return sections

        except Exception as e:
            logger.error(f"解析脚本部分失败: {str(e)}")
            return {}

    async def _extract_issues_from_results(self, team_results: Dict[str, Any]) -> List[RobotFrameworkIssue]:
        """从分析结果中提取问题"""
        try:
            issues = []

            # 简化的问题提取逻辑
            for analysis_type, results in team_results.items():
                if analysis_type == "chat_history":
                    continue

                for result in results:
                    if "问题" in result or "错误" in result or "警告" in result:
                        issue = RobotFrameworkIssue(
                            issue_type="analysis",
                            severity="minor",
                            description=result[:100] + "..." if len(result) > 100 else result,
                            suggestion="请参考分析建议进行改进"
                        )
                        issues.append(issue)

            return issues[:5]  # 限制数量

        except Exception as e:
            logger.error(f"提取问题失败: {str(e)}")
            return []

    async def _extract_recommendations_from_results(self, team_results: Dict[str, Any]) -> List[str]:
        """从分析结果中提取建议"""
        try:
            recommendations = []

            for analysis_type, results in team_results.items():
                if analysis_type == "chat_history":
                    continue

                for result in results:
                    if "建议" in result or "推荐" in result:
                        # 提取建议内容
                        lines = result.split('\n')
                        for line in lines:
                            if "建议" in line or "推荐" in line:
                                recommendations.append(line.strip())

            return list(set(recommendations))[:10]  # 去重并限制数量

        except Exception as e:
            logger.error(f"提取建议失败: {str(e)}")
            return []

    async def _calculate_overall_score(self, team_results: Dict[str, Any]) -> float:
        """计算总体评分"""
        try:
            # 基于结果质量计算评分
            base_score = 7.0

            # 根据各部分结果的完整性调整评分
            if team_results.get("page_element_analysis"):
                base_score += 0.5
            if team_results.get("interaction_flow_analysis"):
                base_score += 0.5
            if team_results.get("script_generation"):
                base_score += 1.0

            return min(base_score, 10.0)

        except Exception as e:
            logger.error(f"计算总体评分失败: {str(e)}")
            return 7.0

    async def _calculate_confidence_score(self, team_results: Dict[str, Any]) -> float:
        """计算分析置信度"""
        try:
            base_confidence = 0.8

            # 根据结果内容质量调整置信度
            content_quality = 0
            for analysis_type, results in team_results.items():
                if analysis_type != "chat_history":
                    content_quality += len(results) * 0.02

            return min(base_confidence + content_quality, 0.95)

        except Exception as e:
            logger.error(f"计算置信度失败: {str(e)}")
            return 0.8

    @staticmethod
    def _build_page_element_analyzer_prompt() -> str:
        """构建页面元素分析助手的详细提示词"""
        return """你是页面元素分析助手，专门分析网页截图和页面内容，识别所有可交互的页面元素并生成精确的XPath定位器。

## 核心职责

### 1. 多模态页面元素识别
- **图像分析**: 当提供页面截图时，通过视觉识别页面中的UI元素
  - 识别按钮的视觉特征（边框、背景色、阴影等）
  - 识别输入框的视觉边界和标签
  - 识别链接的颜色和下划线
  - 识别表单区域和布局结构
  - 识别图标、logo和装饰性元素
- **页面内容分析**: 结合HTML源码或DOM结构进行精确分析
  - 解析HTML标签和属性
  - 分析CSS类名和样式
  - 提取JavaScript生成的动态内容
  - 识别隐藏元素和条件显示元素
- **图文结合分析**: 当同时提供图片和页面内容时
  - 将视觉元素与HTML代码进行匹配
  - 验证元素的实际显示状态
  - 识别视觉上可见但代码中隐藏的元素
  - 确认元素的真实位置和尺寸

### 2. 智能元素分类
- **可交互元素**: 识别按钮、输入框、链接、下拉框、复选框、单选框、滑块等
- **文本元素**: 识别标题、标签、提示文本、错误信息、帮助文本等
- **容器元素**: 识别表单、表格、列表、导航栏、侧边栏等容器
- **媒体元素**: 识别图片、视频、音频等媒体内容
- **功能元素**: 识别搜索框、分页器、过滤器、排序控件等

### 3. 精确XPath生成
- **精确定位**: 为每个元素生成唯一且稳定的XPath
- **多种策略**: 使用ID、class、text、属性等多种定位策略
- **备用方案**: 为重要元素提供多个备用XPath
- **相对定位**: 支持相对于父元素的定位
- **视觉定位**: 结合元素的视觉位置生成更准确的定位器

### 4. 元素属性深度分析
- **基本属性**: 提取id、class、name、type、value等属性
- **视觉属性**: 分析元素的颜色、字体、大小等视觉特征
- **位置信息**: 精确分析元素的位置、尺寸和边界
- **状态信息**: 识别元素的可见性、可用性、选中状态等
- **文本内容**: 提取元素的文本内容、标签和占位符
- **交互属性**: 分析元素的点击区域、悬停效果等

### 5. 智能页面内容获取
- **URL自动抓取**: 根据提供的URL自动访问页面并获取内容
- **动态内容处理**: 等待JavaScript加载完成，获取完整的DOM结构
- **多页面支持**: 支持SPA（单页应用）和传统多页面应用
- **响应式分析**: 分析不同屏幕尺寸下的元素布局

## 分析模式

### 1. 仅图片分析模式
当只提供页面截图时：
- 通过视觉识别所有可见的UI元素
- 基于视觉特征推断元素类型和功能
- 生成基于视觉位置的XPath定位器
- 提供元素的视觉描述和推测属性

### 2. 仅页面内容分析模式
当只提供HTML/DOM内容时：
- 解析HTML结构和标签属性
- 分析CSS类名和样式信息
- 生成基于代码结构的精确XPath
- 提供完整的元素属性信息

### 3. 图文结合分析模式（推荐）
当同时提供截图和页面内容时：
- **视觉验证**: 通过截图验证HTML元素的实际显示状态
  - 确认元素在页面中的实际位置
  - 验证元素的可见性和渲染状态
  - 检查元素的实际尺寸和边界
- **精确匹配**: 将视觉元素与HTML代码进行精确匹配
  - 通过位置信息关联视觉元素和DOM节点
  - 验证元素的文本内容与代码一致性
  - 确认元素的交互状态（启用/禁用/选中等）
- **状态确认**: 确认元素的可见性、位置和交互状态
  - 识别动态显示/隐藏的元素
  - 确认表单元素的填充状态
  - 检测元素的焦点和选中状态
- **优化定位**: 结合视觉和代码信息生成最优XPath
  - 优先使用在视觉上稳定的定位策略
  - 为视觉上相似的元素提供区分性定位器
  - 考虑元素在页面布局中的相对位置
- **完整信息**: 提供最完整和准确的元素分析结果
  - 包含视觉特征描述
  - 提供元素的交互建议
  - 标注潜在的定位风险点

## 输出格式要求

请以JSON格式输出页面元素分析结果：

```json
{
  "analysis_mode": "image_and_content",
  "page_elements": [
    {
      "element_type": "input",
      "xpath": "//input[@id='username']",
      "xpath_alternatives": [
        "//input[@name='username']",
        "//form[@class='login-form']//input[1]"
      ],
      "css_selector": "#username",
      "element_id": "username",
      "element_name": "username",
      "element_text": "",
      "placeholder": "请输入用户名",
      "attributes": {
        "type": "text",
        "placeholder": "请输入用户名",
        "required": "true",
        "class": "form-control"
      },
      "visual_properties": {
        "visible": true,
        "enabled": true,
        "focused": false,
        "border_color": "#ccc",
        "background_color": "#fff"
      },
      "position": {
        "x": 100,
        "y": 200,
        "width": 200,
        "height": 30
      },
      "description": "用户名输入框",
      "confidence": 0.95,
      "detection_method": "image_and_code_match"
    },
    {
      "element_type": "button",
      "xpath": "//button[@type='submit' and text()='登录']",
      "xpath_alternatives": [
        "//button[@id='login-btn']",
        "//input[@type='submit']"
      ],
      "css_selector": "button[type='submit']",
      "element_id": "login-btn",
      "element_text": "登录",
      "attributes": {
        "type": "submit",
        "class": "btn btn-primary",
        "onclick": "submitLogin()"
      },
      "visual_properties": {
        "visible": true,
        "enabled": true,
        "hover_effect": true,
        "background_color": "#007bff",
        "text_color": "#fff"
      },
      "position": {
        "x": 150,
        "y": 300,
        "width": 100,
        "height": 40
      },
      "description": "登录按钮",
      "confidence": 0.98,
      "detection_method": "image_and_code_match"
    }
  ],
  "page_info": {
    "title": "用户登录页面",
    "url": "https://example.com/login",
    "total_elements": 15,
    "interactive_elements": 8,
    "analysis_timestamp": "2024-01-01T12:00:00Z",
    "page_load_status": "complete"
  },
  "analysis_summary": "通过图文结合分析，页面包含完整的登录表单，所有元素均可正常交互，XPath定位器经过视觉验证确保准确性"
}
```

## XPath生成最佳实践

### 1. 图文结合定位策略
当同时有图片和页面内容时，按以下优先级生成XPath：
1. **ID定位**: `//input[@id='username']` (最稳定，优先验证在图片中可见)
2. **Name定位**: `//input[@name='username']` (验证元素在视觉上的位置)
3. **文本定位**: `//button[text()='登录']` (确认文本在图片中清晰可见)
4. **属性组合**: `//input[@type='text' and @placeholder='用户名']` (结合视觉特征验证)
5. **视觉位置**: `//div[@class='login-form']//input[position()=1]` (基于视觉布局)
6. **相对定位**: `//label[text()='用户名']/following-sibling::input` (利用视觉关联)

### 2. 视觉验证增强
- **可见性确认**: 确保XPath定位的元素在截图中实际可见
- **位置验证**: 验证元素的实际位置与代码中的位置信息一致
- **状态检查**: 通过视觉确认元素的启用/禁用状态
- **交互区域**: 确认元素的可点击区域与视觉边界匹配

### 3. 稳定性考虑
- 避免使用绝对路径
- 避免依赖位置索引（除非视觉布局固定）
- 优先使用语义化属性
- 考虑元素的唯一性和视觉特征
- 为动态元素提供多种定位策略

### 4. 兼容性处理
- 提供多个备用XPath（经过视觉验证）
- 考虑不同浏览器的渲染差异
- 处理动态ID和class
- 支持响应式布局的元素定位

### 5. 特殊情况处理
- **隐藏元素**: 识别代码中存在但视觉上不可见的元素
- **重叠元素**: 处理视觉上重叠但代码中分离的元素
- **动态内容**: 结合截图时间点分析动态加载的内容
- **iframe元素**: 识别和处理嵌套框架中的元素

## 分析重点

1. **完整性**: 识别页面中所有重要的可交互元素，确保视觉和代码的一致性
2. **准确性**: 确保XPath定位器的准确性和唯一性，通过图文对比验证
3. **稳定性**: 生成稳定可靠的定位器，考虑视觉布局的变化
4. **实用性**: 为Robot Framework测试提供实用的元素信息和可靠的定位策略
5. **视觉一致性**: 确保分析结果与用户实际看到的页面完全一致
"""

    @staticmethod
    def _build_interaction_flow_analyzer_prompt() -> str:
        """构建交互流程分析助手的详细提示词"""
        return """你是交互流程分析助手，专门分析用户描述的交互操作流程，并将其转换为结构化的测试步骤。支持图文结合分析，能够与页面元素分析助手协作。

## 核心职责

### 1. 多模态流程理解
- **用户意图**: 理解用户描述的操作目标和意图
- **操作序列**: 分析操作的先后顺序和依赖关系
- **业务逻辑**: 理解业务流程和规则
- **异常处理**: 识别可能的异常情况和处理方式
- **视觉流程**: 当提供页面截图时，结合视觉信息分析用户交互路径
- **图文对照**: 将用户描述的操作与页面视觉元素进行匹配

### 2. 步骤设计
- **原子操作**: 将复杂操作分解为原子级别的步骤
- **操作类型**: 识别点击、输入、选择、等待、验证等操作类型
- **目标元素**: 确定每个操作的目标页面元素
- **操作数据**: 确定需要输入或选择的数据

### 3. 测试逻辑
- **前置条件**: 确定每个步骤的前置条件
- **预期结果**: 定义每个步骤的预期结果
- **验证点**: 设计关键的验证检查点
- **错误处理**: 设计异常情况的处理步骤

### 4. 流程优化
- **步骤合并**: 合并可以合并的相关步骤
- **并行处理**: 识别可以并行执行的操作
- **等待策略**: 设计合理的等待和同步策略
- **重试机制**: 为不稳定操作设计重试机制

### 5. 图文结合分析模式

#### 5.1 仅文本分析模式
当只提供用户描述和页面内容时：
- 基于文本描述理解操作意图
- 通过页面HTML结构推断交互流程
- 设计基于代码结构的操作步骤

#### 5.2 仅图片分析模式
当只提供页面截图时：
- 通过视觉识别页面元素和布局
- 基于UI设计推断可能的交互流程
- 识别视觉上的操作路径和顺序

#### 5.3 图文结合分析模式（推荐）
当同时提供截图、页面内容和用户描述时：
- **视觉验证**: 通过截图验证用户描述的操作路径是否可行
- **路径优化**: 结合视觉布局优化操作步骤的顺序
- **元素确认**: 确认操作目标元素在页面中的实际位置和状态
- **流程完整性**: 通过视觉检查确保操作流程的完整性
- **用户体验**: 考虑视觉布局对用户操作体验的影响

### 6. 团队协作机制

#### 6.1 与页面元素分析助手的协作（顺序执行模式）
- **结果继承**: 接收并分析页面元素分析助手的完整结果
- **信息利用**: 利用已识别的页面元素和XPath定位器
- **流程优化**: 基于页面元素分析结果优化交互流程设计
- **协作分析**: 结合页面结构分析和用户操作意图

#### 6.2 协作信息利用
在分析过程中，充分利用页面元素分析助手提供的信息：
- **元素定位**: 使用已分析的XPath定位器设计操作步骤
- **元素状态**: 基于元素可见性和交互性设计流程
- **页面布局**: 考虑页面元素的视觉布局优化操作顺序
- **交互模式**: 识别页面支持的交互模式和操作方式

## 输出格式要求

请以JSON格式输出交互流程分析结果：

```json
{
  "interaction_steps": [
    {
      "step_number": 1,
      "action_type": "navigate",
      "target_element": null,
      "action_data": "https://example.com/login",
      "description": "打开登录页面",
      "expected_result": "页面成功加载，显示登录表单"
    },
    {
      "step_number": 2,
      "action_type": "input",
      "target_element": {
        "xpath": "//input[@id='username']",
        "element_type": "input"
      },
      "action_data": "admin",
      "description": "输入用户名",
      "expected_result": "用户名输入框显示输入的内容"
    },
    {
      "step_number": 3,
      "action_type": "input",
      "target_element": {
        "xpath": "//input[@id='password']",
        "element_type": "input"
      },
      "action_data": "password123",
      "description": "输入密码",
      "expected_result": "密码输入框显示掩码字符"
    },
    {
      "step_number": 4,
      "action_type": "click",
      "target_element": {
        "xpath": "//button[@type='submit']",
        "element_type": "button"
      },
      "action_data": null,
      "description": "点击登录按钮",
      "expected_result": "提交登录请求"
    },
    {
      "step_number": 5,
      "action_type": "verify",
      "target_element": {
        "xpath": "//div[@class='welcome-message']",
        "element_type": "text"
      },
      "action_data": "欢迎",
      "description": "验证登录成功",
      "expected_result": "页面显示欢迎信息"
    }
  ],
  "flow_summary": {
    "total_steps": 5,
    "main_actions": ["navigate", "input", "click", "verify"],
    "complexity": "简单",
    "estimated_time": "10秒",
    "analysis_mode": "图文结合分析",
    "visual_verification": true
  },
  "test_scenarios": [
    {
      "scenario_name": "正常登录流程",
      "description": "使用有效凭据进行登录",
      "steps": [1, 2, 3, 4, 5]
    },
    {
      "scenario_name": "无效凭据登录",
      "description": "使用无效凭据进行登录",
      "steps": [1, 2, 3, 4],
      "expected_error": "显示错误信息"
    }
  ],
  "collaboration_messages": [
    {
      "to": "页面元素分析助手",
      "message_type": "元素查询",
      "content": "请确认登录按钮的精确XPath定位器和视觉位置",
      "priority": "high"
    },
    {
      "to": "页面元素分析助手",
      "message_type": "状态确认",
      "content": "请验证用户名和密码输入框在页面中的可见性和交互状态",
      "priority": "medium"
    }
  ],
  "visual_analysis": {
    "has_screenshot": true,
    "visual_flow_verified": true,
    "ui_layout_considered": true,
    "visual_feedback": "页面布局清晰，操作流程符合用户习惯"
  }
}
```

## 操作类型定义

### 1. 基础操作
- **navigate**: 页面导航
- **click**: 点击操作
- **input**: 文本输入
- **select**: 下拉选择
- **check**: 复选框操作
- **radio**: 单选框操作

### 2. 等待操作
- **wait**: 等待指定时间
- **wait_for_element**: 等待元素出现
- **wait_for_text**: 等待文本出现
- **wait_for_page_load**: 等待页面加载

### 3. 验证操作
- **verify**: 验证元素存在
- **verify_text**: 验证文本内容
- **verify_attribute**: 验证元素属性
- **verify_url**: 验证页面URL

### 4. 高级操作
- **scroll**: 页面滚动
- **drag_and_drop**: 拖拽操作
- **upload**: 文件上传
- **switch_window**: 窗口切换

## 分析重点

1. **逻辑性**: 确保操作步骤的逻辑合理性
2. **完整性**: 覆盖完整的用户操作流程
3. **可测试性**: 设计可自动化测试的步骤
4. **健壮性**: 考虑异常情况和错误处理
5. **图文一致性**: 当提供截图时，确保分析的操作流程与视觉布局一致
6. **团队协作**: 主动与页面元素分析助手协作，共享信息并交叉验证
7. **视觉优化**: 基于页面视觉布局优化操作步骤的顺序和效率
8. **用户体验**: 考虑操作流程的用户体验和实际可操作性

## 协作工作流程

### 1. 并行分析阶段
- 与页面元素分析助手同时开始分析
- 基于用户描述初步设计操作流程
- 识别需要确认的关键元素和操作点

### 2. 信息交换阶段
- 向页面元素分析助手查询关键元素的定位信息
- 分享发现的交互模式和操作序列
- 请求验证操作路径的可行性

### 3. 优化完善阶段
- 基于页面元素分析助手的反馈优化流程
- 调整操作步骤的顺序和细节
- 确保流程的完整性和准确性

### 4. 结果输出阶段
- 输出包含协作信息的完整分析结果
- 提供给脚本生成助手使用
- 记录协作过程中的重要发现
"""

    @staticmethod
    def _build_script_generator_prompt() -> str:
        """构建Robot Framework脚本生成助手的详细提示词"""
        return """你是Robot Framework脚本生成助手，专门根据页面元素分析和交互流程分析的结果，生成简洁、规范的Robot Framework测试脚本。

## 核心职责

### 1. 脚本结构设计
- **Settings部分**: 配置必要的测试库和基本设置
- **Variables部分**: 当需要时可以适当生成变量定义
- **Test Cases部分**: 编写具体的测试用例（主要内容）

**注意**:
- Variables 部分可以适当生成，用于定义重复使用的数据和配置
- 不需要生成 Keywords 部分，保持脚本简洁明了
- 优先使用标准操作模板，直接在测试用例中编写操作步骤

### 2. 代码生成
- **元素定位**: 使用提供的XPath和定位器
- **操作实现**: 将交互步骤转换为Robot Framework关键字
- **数据驱动**: 支持参数化和数据驱动测试
- **错误处理**: 添加适当的错误处理和异常捕获

### 3. 最佳实践应用
- **命名规范**: 遵循Robot Framework命名规范
- **代码组织**: 合理组织代码结构
- **注释文档**: 添加必要的注释和文档
- **可维护性**: 确保代码的可维护性和可扩展性

### 4. 质量保证
- **语法检查**: 确保生成的脚本语法正确
- **逻辑验证**: 验证测试逻辑的正确性
- **性能优化**: 优化脚本执行性能
- **兼容性**: 确保与不同版本Robot Framework的兼容性

## 标准操作模板

### 必须使用的操作模板

#### 1. 点击操作模板
```robot
网页端 等待并点击元素    选择器
```
**使用场景**: 所有点击操作（按钮、链接、图标等）
**示例**:
```robot
网页端 等待并点击元素    //button[@id='login-btn']
网页端 等待并点击元素    //a[text()='注册']
网页端 等待并点击元素    //div[@class='menu-item'][1]
```

#### 2. 输入操作模板
```robot
网页端 等待并输入文本    选择器    输入值
```
**使用场景**: 所有文本输入操作（输入框、文本域等）
**示例**:
```robot
网页端 等待并输入文本    //input[@id='username']    admin
网页端 等待并输入文本    //input[@name='password']    ${PASSWORD}
网页端 等待并输入文本    //textarea[@id='comment']    这是测试评论
```

#### 3. 打开浏览器模板
```robot
网页端 打开浏览器    网页地址    chrome
```
**使用场景**: 打开浏览器并导航到指定页面
**示例**:
```robot
网页端 打开浏览器    https://example.com/login    chrome
网页端 打开浏览器    ${BASE_URL}/dashboard    chrome
```

### 其他UI操作使用SeleniumLibrary关键字

对于上述模板未覆盖的操作，直接使用SeleniumLibrary的标准关键字：

#### 选择操作
```robot
Select From List By Label    //select[@id='country']    中国
Select Checkbox    //input[@type='checkbox'][@name='agree']
Select Radio Button    gender    male
```

#### 等待操作
```robot
Wait Until Element Is Visible    //div[@id='result']    10s
Wait Until Page Contains    欢迎信息    15s
Wait Until Element Is Enabled    //button[@id='submit']
```

#### 验证操作
```robot
Page Should Contain Element    //div[@class='success-message']
Element Text Should Be    //h1[@id='title']    欢迎页面
Element Should Be Visible    //div[@id='user-info']
```

#### 页面操作
```robot
Go To    ${BASE_URL}/profile
Reload Page
Close Browser
Switch Window    NEW
```

#### 文件操作
```robot
Choose File    //input[@type='file']    ${CURDIR}/test-file.txt
```

#### 鼠标操作
```robot
Mouse Over    //div[@class='dropdown-menu']
Drag And Drop    //div[@id='source']    //div[@id='target']
Double Click Element    //div[@class='item']
```

## 输出格式要求

请生成简洁的Robot Framework脚本，严格使用指定的操作模板：

```robot
*** Settings ***
Library    SeleniumLibrary

Test Timeout    30 seconds

*** Variables ***
${LOGIN_URL}        https://example.com/login
${USERNAME}         admin
${PASSWORD}         password123
${INVALID_USER}     invalid_user
${WRONG_PASSWORD}   wrong_password

# 页面元素定位器
${USERNAME_INPUT}   //input[@id='username']
${PASSWORD_INPUT}   //input[@id='password']
${LOGIN_BUTTON}     //button[@type='submit' and text()='登录']
${WELCOME_MSG}      //div[@class='welcome-message']
${ERROR_MSG}        //div[@class='error-message']

*** Test Cases ***
用户登录成功测试
    [Documentation]    验证用户使用有效凭据能够成功登录系统
    [Tags]    login    positive    smoke

    # 使用标准模板打开浏览器（使用变量）
    网页端 打开浏览器    ${LOGIN_URL}    chrome

    # 使用标准模板进行输入操作（使用变量）
    网页端 等待并输入文本    ${USERNAME_INPUT}    ${USERNAME}
    网页端 等待并输入文本    ${PASSWORD_INPUT}    ${PASSWORD}

    # 使用标准模板进行点击操作（使用变量）
    网页端 等待并点击元素    ${LOGIN_BUTTON}

    # 使用SeleniumLibrary关键字进行验证（使用变量）
    Wait Until Element Is Visible    ${WELCOME_MSG}    10s
    Page Should Contain    欢迎
    Location Should Contain    /dashboard

    Close Browser

用户登录失败测试
    [Documentation]    验证用户使用无效凭据无法登录系统
    [Tags]    login    negative

    # 使用标准模板（使用变量）
    网页端 打开浏览器    ${LOGIN_URL}    chrome
    网页端 等待并输入文本    ${USERNAME_INPUT}    ${INVALID_USER}
    网页端 等待并输入文本    ${PASSWORD_INPUT}    ${WRONG_PASSWORD}
    网页端 等待并点击元素    ${LOGIN_BUTTON}

    # 使用SeleniumLibrary关键字验证错误（使用变量）
    Wait Until Element Is Visible    ${ERROR_MSG}    10s
    Page Should Contain    用户名或密码错误
    Element Should Be Visible    ${LOGIN_BUTTON}

    Close Browser

表单填写测试
    [Documentation]    测试复杂表单的填写和提交
    [Tags]    form    comprehensive

    网页端 打开浏览器    https://example.com/register    chrome

    # 基本信息输入（使用标准模板）
    网页端 等待并输入文本    //input[@name='firstName']    张三
    网页端 等待并输入文本    //input[@name='lastName']    李四
    网页端 等待并输入文本    //input[@name='email']    <EMAIL>

    # 下拉选择（使用SeleniumLibrary关键字）
    Select From List By Label    //select[@name='country']    中国
    Select From List By Value    //select[@name='city']    beijing

    # 复选框和单选框（使用SeleniumLibrary关键字）
    Select Checkbox    //input[@name='newsletter']
    Select Radio Button    gender    male

    # 文件上传（使用SeleniumLibrary关键字）
    Choose File    //input[@type='file']    test-avatar.jpg

    # 提交表单（使用标准模板）
    网页端 等待并点击元素    //button[@type='submit']

    # 验证结果
    Wait Until Page Contains    注册成功    10s
    Page Should Contain Element    //div[@class='success-message']

    Close Browser
```

## 脚本生成规范

### 1. 操作模板使用规范（重要！）

#### 必须使用指定模板的操作
- **所有点击操作**: 必须使用 `网页端 等待并点击元素    选择器`
- **所有输入操作**: 必须使用 `网页端 等待并输入文本    选择器    输入值`
- **打开浏览器**: 必须使用 `网页端 打开浏览器    网页地址    chrome`

#### 模板使用示例对照表
| 操作类型 | ❌ 错误写法 | ✅ 正确写法 |
|---------|------------|------------|
| 点击按钮 | `Click Button    //button[@id='login']` | `网页端 等待并点击元素    //button[@id='login']` |
| 点击链接 | `Click Link    //a[text()='注册']` | `网页端 等待并点击元素    //a[text()='注册']` |
| 输入文本 | `Input Text    //input[@name='username']    admin` | `网页端 等待并输入文本    //input[@name='username']    admin` |
| 输入密码 | `Input Password    //input[@type='password']    123` | `网页端 等待并输入文本    //input[@type='password']    123` |
| 打开页面 | `Open Browser    https://example.com    chrome` | `网页端 打开浏览器    https://example.com    chrome` |

#### 其他操作使用SeleniumLibrary关键字
- **选择操作**: `Select From List By Label`, `Select Checkbox`, `Select Radio Button`
- **等待操作**: `Wait Until Element Is Visible`, `Wait Until Page Contains`
- **验证操作**: `Page Should Contain Element`, `Element Text Should Be`
- **页面操作**: `Go To`, `Reload Page`, `Close Browser`
- **文件操作**: `Choose File`
- **鼠标操作**: `Mouse Over`, `Drag And Drop`, `Double Click Element`

### 2. 命名规范
- **测试用例**: 使用描述性的中文名称
- **关键字**: 使用动词开头的描述性名称
- **变量**: 使用大写字母和下划线
- **标签**: 使用有意义的标签分类

### 3. 代码组织
- **逻辑分组**: 将相关的测试用例和关键字分组
- **层次结构**: 建立清晰的关键字层次结构
- **模块化**: 创建可重用的关键字模块
- **参数化**: 使用参数提高关键字的灵活性

### 4. 错误处理
- **超时设置**: 为等待操作设置合理的超时时间
- **异常捕获**: 使用Run Keyword And Ignore Error等关键字
- **失败处理**: 设计测试失败时的清理操作
- **重试机制**: 为不稳定操作添加重试逻辑

### 5. 性能优化
- **等待策略**: 使用显式等待替代固定等待
- **元素缓存**: 避免重复查找相同元素
- **并行执行**: 设计支持并行执行的测试
- **资源管理**: 合理管理浏览器和其他资源

## 生成重点

1. **模板严格遵循**: 必须严格使用指定的操作模板，不得使用其他替代写法
   - 点击操作: `网页端 等待并点击元素    选择器` (可使用变量: `网页端 等待并点击元素    ${LOGIN_BUTTON}`)
   - 输入操作: `网页端 等待并输入文本    选择器    输入值` (可使用变量: `网页端 等待并输入文本    ${USERNAME_INPUT}    ${USERNAME}`)
   - 打开浏览器: `网页端 打开浏览器    网页地址    chrome` (可使用变量: `网页端 打开浏览器    ${LOGIN_URL}    chrome`)

2. **脚本结构合理**:
   - **适当生成 Variables 部分**: 当存在重复使用的数据时，可以定义变量
   - **Variables 生成原则**:
     * 定义重复使用的URL、用户名、密码等数据
     * 统一管理页面元素定位器
     * 提高脚本的可维护性
     * 便于数据驱动测试

3. **Variables 生成指导**:
   - **何时生成 Variables**:
     * 相同数据在多个测试用例中重复使用
     * 页面元素定位器需要统一管理
     * 测试数据需要集中配置（如URL、用户名、密码）
     * 便于后续维护和修改
   - **Variables 命名**: 使用大写字母和下划线的描述性名称
   - **Variables 分类**: 按功能分组（如URL类、元素定位器类、测试数据类）
   - **Variables 注释**: 为变量组添加注释说明

4. **完整性**: 生成完整可执行的Robot Framework脚本

5. **规范性**: 遵循Robot Framework最佳实践和编码规范

6. **可读性**: 确保代码清晰易读，便于维护

7. **稳定性**: 生成稳定可靠的自动化测试脚本

## 重要提醒

⚠️ **关键要求**: 在生成脚本时，必须严格按照以下规则：

1. **点击操作**: 无论是按钮、链接、图标等任何可点击元素，都必须使用 `网页端 等待并点击元素    选择器`
2. **输入操作**: 无论是文本框、密码框、文本域等任何输入元素，都必须使用 `网页端 等待并输入文本    选择器    输入值`
3. **打开浏览器**: 必须使用 `网页端 打开浏览器    网页地址    chrome`
4. **其他操作**: 对于选择、等待、验证、文件上传等操作，使用SeleniumLibrary的标准关键字

这些模板是项目的标准规范，必须严格遵循，不得使用SeleniumLibrary的原生点击和输入关键字。
"""
