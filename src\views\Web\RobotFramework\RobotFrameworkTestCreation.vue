<template>
  <div class="robot-framework-test-creation">
    <el-card class="main-card">
      <div slot="header" class="card-header">
        <span>Robot Framework 测试创建</span>
      </div>

      <el-form
        ref="form"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        @submit.native.prevent="handleSubmit"
      >
        <!-- 页面URL -->
        <el-form-item label="页面URL" prop="page_url">
          <el-input
            v-model="formData.page_url"
            placeholder="https://example.com"
            prefix-icon="el-icon-link"
          />
        </el-form-item>

        <!-- 上传页面截图 -->
        <el-form-item label="页面截图" prop="file">
          <div class="upload-section">
            <el-upload
              ref="upload"
              :before-upload="handleImageUpload"
              :show-file-list="false"
              accept="image/*"
              action="#"
            >
              <el-button size="small" type="primary" icon="el-icon-upload">
                {{ uploadedFile ? uploadedFile.name : '选择图片' }}
              </el-button>
            </el-upload>

            <el-button
              v-if="uploadedFile"
              size="small"
              type="danger"
              icon="el-icon-delete"
              @click="handleClearFile"
              style="margin-left: 10px;"
            >
              清空文件
            </el-button>
          </div>
          <div class="form-tip">上传页面截图用于辅助元素分析（可选）</div>
        </el-form-item>

        <!-- 测试描述 -->
        <el-form-item label="测试描述" prop="interaction_description">
          <el-input
            v-model="formData.interaction_description"
            type="textarea"
            :rows="4"
            placeholder="描述要测试的功能和操作步骤，例如：登录功能测试，输入用户名密码，点击登录按钮，验证登录成功..."
          />
          <div class="form-tip">描述要测试的功能和操作步骤（可选）</div>
        </el-form-item>

        <!-- 附加上下文 -->
        <el-form-item label="附加上下文" prop="additional_context">
          <el-input
            v-model="formData.additional_context"
            type="textarea"
            :rows="3"
            placeholder="提供额外的测试上下文，如特殊的验证规则、数据准备要求等..."
          />
          <div class="form-tip">提供额外的测试上下文信息（可选）</div>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="isAnalyzing"
            @click="handleSubmit"
            icon="el-icon-view"
          >
            {{ isAnalyzing ? '分析中...' : '开始分析' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 分析结果分隔线 -->
      <el-divider v-if="isAnalyzing || analysisResult || currentSessionId">
        <span>分析结果</span>
      </el-divider>

      <!-- 流式显示组件 -->
      <streaming-display
        v-if="currentSessionId"
        :session-id="currentSessionId"
        :is-active="isAnalyzing || preserveStreamingContent"
        :stream-endpoint="`/api/v1/web/robot-framework/stream/${currentSessionId}`"
        @analysis-complete="handleStreamingComplete"
        @final-complete="handleFinalComplete"
        @error="handleStreamingError"
      />

      <!-- 分析结果显示区域 -->
      <el-card v-if="analysisResult" class="result-card">
        <div slot="header">
          <span>Robot Framework脚本</span>
        </div>

        <el-input
          v-model="analysisResult.robot_content"
          type="textarea"
          :rows="20"
          readonly
          class="result-textarea"
        />
      </el-card>
    </el-card>
  </div>
</template>

<script>
import StreamingDisplay from '@/components/StreamingDisplay/StreamingDisplay.vue'
import { robotFrameworkApi } from '@/services/api'

export default {
  name: 'RobotFrameworkTestCreation',

  components: {
    StreamingDisplay
  },

  data() {
    return {
      formData: {
        page_url: '',
        interaction_description: '',
        additional_context: ''
      },

      formRules: {
        page_url: [
          { required: true, message: '请输入页面URL', trigger: 'blur' },
          { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
        ]
      },

      uploadedFile: null,
      isAnalyzing: false,
      currentSessionId: '',
      preserveStreamingContent: false,
      analysisResult: null
    }
  },

  methods: {
    // 处理文件上传
    handleImageUpload(file) {
      // 验证文件类型
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        this.$message.error('只能上传图片文件！')
        return false
      }

      // 验证文件大小（5MB限制）
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB！')
        return false
      }

      this.uploadedFile = file
      this.$message.success(`${file.name} 文件上传成功`)
      return false // 阻止自动上传
    },

    // 清空上传的文件
    handleClearFile() {
      this.uploadedFile = null
      this.$message.success('已清空上传的文件')
    },

    // 处理表单提交
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) {
          return false
        }

        try {
          this.isAnalyzing = true
          this.preserveStreamingContent = true

          // 生成会话ID
          this.currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)

          // 准备请求数据
          const requestData = {
            ...this.formData,
            file: this.uploadedFile,
            save_to_database: false, // 根据用户偏好，不保存到数据库
            analysis_type: 'comprehensive'
          }

          console.log('发送分析请求:', requestData)

          // 调用API
          const response = await robotFrameworkApi.analyze(requestData)
          console.log('收到API响应:', response)

          if (response && response.session_id) {
            this.currentSessionId = response.session_id
            this.$message.success('分析请求已提交，正在处理...')
          }
        } catch (error) {
          console.error('分析失败:', error)
          this.isAnalyzing = false
          this.$message.error(`分析失败: ${error.message || '未知错误'}`)
        }
      })
    },

    // 处理流式显示完成
    handleStreamingComplete(result) {
      console.log('流式显示完成:', result)
      this.isAnalyzing = false
      this.preserveStreamingContent = false

      if (result && result.content) {
        this.analysisResult = {
          session_id: this.currentSessionId,
          robot_content: result.content
        }
      }
    },

    // 处理最终完成（当is_final为true时触发）
    handleFinalComplete(result) {
      console.log('收到最终完成消息:', result)

      if (result.content) {
        this.analysisResult = this.analysisResult
          ? {
              ...this.analysisResult,
              robot_content: result.content
            }
          : {
              session_id: this.currentSessionId,
              robot_content: result.content
            }
      }

      this.$message.success('Robot Framework脚本生成完成！')
    },

    // 处理流式显示错误
    handleStreamingError(error) {
      console.error('流式显示错误:', error)
      this.isAnalyzing = false
      this.$message.error(`分析错误: ${error}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.robot-framework-test-creation {
  .main-card {
    .card-header {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .upload-section {
      display: flex;
      align-items: center;
    }

    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }

    .result-card {
      margin-top: 20px;

      .result-textarea {
        ::v-deep .el-textarea__inner {
          font-family: 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.4;
          background-color: #f8f9fa;
        }
      }
    }
  }
}

// 全局样式调整
::v-deep .el-form-item__label {
  font-weight: 500;
}

::v-deep .el-card__header {
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

::v-deep .el-divider__text {
  background-color: #fff;
  color: #409eff;
  font-weight: 500;
}
</style>
