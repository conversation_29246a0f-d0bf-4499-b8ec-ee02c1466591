{"name": "frontend-vue2", "version": "1.0.0", "description": "基于Vue2和Element-UI的前端工程", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve --mode development", "build:prod": "vue-cli-service build --mode production", "setup": "node scripts/setup.js"}, "engines": {"node": ">=14.0.0"}, "dependencies": {"vue": "^2.6.14", "vue-router": "^3.6.5", "vuex": "^3.6.2", "element-ui": "^2.15.13", "axios": "^1.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^7.20.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}