# 快速启动指南

## 环境要求

确保您的系统满足以下要求：
- **Node.js**: >= 14.0.0
- **npm**: >= 6.0.0

## 快速开始

### 1. 检查 Node.js 版本
```bash
node --version
# 应该显示 v14.x.x 或更高版本
```

### 2. 进入项目目录
```bash
cd frontend-vue
```

### 3. 运行设置脚本（推荐）
```bash
npm run setup
```

这个脚本会：
- 检查 Node.js 版本
- 自动安装依赖
- 验证环境配置
- 显示可用命令

### 4. 启动开发服务器
```bash
npm run serve
# 或者
npm run dev
```

### 5. 打开浏览器
访问 http://localhost:3000

## 手动安装（如果设置脚本失败）

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run serve
```

## 项目功能

### Robot Framework 测试创建页面
- 访问路径: `/web/robot-framework-create`
- 功能:
  - 输入页面URL（必填）
  - 上传页面截图（可选，支持清空）
  - 输入测试描述（可选）
  - 输入附加上下文（可选）
  - 实时查看分析结果
  - 查看生成的Robot Framework脚本

## 开发说明

### 项目结构
```
src/
├── components/           # 公共组件
│   ├── Layout/          # 布局组件
│   └── StreamingDisplay/ # 流式显示组件
├── views/               # 页面组件
│   └── Web/             # Web测试页面
├── router/              # 路由配置
├── store/               # 状态管理
├── services/            # API服务
└── styles/              # 样式文件
```

### API配置
- 开发环境: `http://localhost:8000/api`
- 生产环境: `/api`

确保后端服务运行在8000端口。

### 常用命令
```bash
# 开发
npm run serve          # 启动开发服务器
npm run dev           # 同上

# 构建
npm run build         # 生产构建
npm run build:prod    # 同上

# 代码检查
npm run lint          # ESLint检查

# 设置
npm run setup         # 项目初始化设置
```

## 故障排除

### 1. 端口冲突
如果3000端口被占用，Vue CLI会自动选择其他端口。

### 2. 依赖安装失败
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules
npm install
```

### 3. Node.js版本过低
请升级到Node.js 14或更高版本：
- 访问 https://nodejs.org/
- 下载并安装最新的LTS版本

### 4. API连接问题
检查：
- 后端服务是否在8000端口运行
- 网络连接是否正常
- 防火墙设置

## 技术支持

如果遇到问题，请检查：
1. Node.js版本是否符合要求
2. 依赖是否正确安装
3. 后端服务是否正常运行
4. 浏览器控制台是否有错误信息
