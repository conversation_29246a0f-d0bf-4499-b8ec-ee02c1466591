import axios from 'axios'
import { Message } from 'element-ui'

// 创建 axios 实例
const api = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加 token 等认证信息
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          Message.error(data.message || '请求参数错误')
          break
        case 401:
          Message.error('未授权，请重新登录')
          // 可以在这里处理登录跳转
          break
        case 403:
          Message.error('拒绝访问')
          break
        case 404:
          Message.error('请求的资源不存在')
          break
        case 500:
          Message.error('服务器内部错误')
          break
        default:
          Message.error(data.message || '请求失败')
      }
    } else if (error.request) {
      Message.error('网络错误，请检查网络连接')
    } else {
      Message.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API 方法
export const robotFrameworkApi = {
  // 分析 Robot Framework
  analyze(data) {
    const formData = new FormData()
    
    // 添加文件（如果有）
    if (data.file) {
      formData.append('file', data.file)
    }
    
    // 添加基本参数
    formData.append('page_url', data.page_url)
    if (data.interaction_description) {
      formData.append('interaction_description', data.interaction_description)
    }
    if (data.additional_context) {
      formData.append('additional_context', data.additional_context)
    }
    
    return api.post('/v1/web/robot-framework/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export default api
