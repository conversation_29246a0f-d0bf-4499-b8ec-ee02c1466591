# Frontend Vue2 - UI自动化测试平台

基于Vue2和Element-UI架构的前端工程，用于UI自动化测试平台。

## 🚀 技术栈

- **Vue 2.6.14** - 渐进式JavaScript框架
- **Element-UI 2.15.13** - 基于Vue 2.0的桌面端组件库
- **Vue Router 3.x** - Vue.js官方路由管理器
- **Vuex 3.x** - Vue.js的状态管理模式
- **Axios** - 基于Promise的HTTP库
- **Sass** - CSS预处理器

## 📋 环境要求

- **Node.js**: >= 14.0.0
- **npm**: >= 6.0.0

## 🏗️ 项目结构

```
frontend-vue2/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 公共组件
│   │   ├── Layout/         # 布局组件
│   │   └── StreamingDisplay/ # 流式显示组件
│   ├── views/              # 页面组件
│   │   └── Web/            # Web测试相关页面
│   ├── router/             # 路由配置
│   ├── store/              # Vuex状态管理
│   ├── services/           # API服务
│   ├── styles/             # 全局样式
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── scripts/                # 工具脚本
├── .env.development        # 开发环境配置
├── .env.production         # 生产环境配置
├── vue.config.js           # Vue CLI配置
└── package.json            # 项目依赖
```

## 🛠️ 快速开始

### 1. 安装依赖

```bash
# 进入项目目录
cd frontend-vue2

# 使用设置脚本（推荐）
npm run setup

# 或手动安装
npm install
```

### 2. 启动开发服务器

```bash
npm run serve
# 或
npm run dev
```

开发服务器将在 http://localhost:3000 启动

### 3. 构建生产版本

```bash
npm run build
# 或
npm run build:prod
```

## ✨ 功能特性

### 🤖 Robot Framework 测试创建
- 页面URL输入（必填，带URL验证）
- 页面截图上传（可选，支持清空功能）
- 测试描述输入（可选）
- 附加上下文输入（可选）
- 实时流式显示分析结果
- 最终Robot Framework脚本展示

### 📡 StreamingDisplay组件
- 支持Server-Sent Events (SSE)
- 实时消息显示和格式化
- 自动滚动和错误处理
- 支持自定义流式端点
- 完整的连接状态管理
- 支持重连机制

### 🎨 响应式布局系统
- 侧边栏导航
- 主内容区域
- 卡片式设计
- 移动端适配

## 🔧 开发配置

### API集成
- **开发环境**: `http://localhost:8000/api`
- **生产环境**: `/api`

### 样式系统
- 使用Sass作为CSS预处理器
- 定义了完整的设计变量系统
- 提供了常用的工具类
- 自定义了Element-UI主题

## 📝 可用命令

```bash
npm run serve      # 启动开发服务器
npm run dev        # 同上
npm run build      # 生产环境构建
npm run build:prod # 同上
npm run lint       # 代码检查
npm run setup      # 项目初始化设置
```

## 🌐 浏览器支持

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 🚨 故障排除

### 端口冲突
如果3000端口被占用，Vue CLI会自动选择其他端口。

### 依赖安装失败
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules
npm install
```

### Node.js版本过低
请升级到Node.js 14或更高版本：
- 访问 https://nodejs.org/
- 下载并安装最新的LTS版本

### API连接问题
检查：
- 后端服务是否在8000端口运行
- 网络连接是否正常
- 防火墙设置

## 📞 技术支持

如果遇到问题，请检查：
1. Node.js版本是否符合要求
2. 依赖是否正确安装
3. 后端服务是否正常运行
4. 浏览器控制台是否有错误信息
