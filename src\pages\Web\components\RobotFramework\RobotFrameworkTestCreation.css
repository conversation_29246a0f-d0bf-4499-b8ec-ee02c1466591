/* Robot Framework测试创建页面样式 */
.robot-framework-test-creation {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
}

.robot-framework-test-creation .main-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.robot-framework-test-creation .ant-card-head {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
  border-bottom: none;
}

.robot-framework-test-creation .ant-card-head-title {
  color: white;
  font-weight: 600;
}

.robot-framework-test-creation .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.robot-framework-test-creation .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
  color: #ff4d4f;
}

.robot-framework-test-creation .ant-upload {
  width: 100%;
}

.robot-framework-test-creation .ant-upload .ant-btn {
  width: 100%;
  height: 40px;
  border-style: dashed;
  border-color: #d9d9d9;
  background: #fafafa;
  transition: all 0.3s;
}

.robot-framework-test-creation .ant-upload .ant-btn:hover {
  border-color: #722ed1;
  color: #722ed1;
}

.robot-framework-test-creation .ant-input,
.robot-framework-test-creation .ant-input-affix-wrapper {
  border-radius: 6px;
  border-color: #d9d9d9;
  transition: all 0.3s;
}

.robot-framework-test-creation .ant-input:focus,
.robot-framework-test-creation .ant-input-affix-wrapper:focus,
.robot-framework-test-creation .ant-input:hover,
.robot-framework-test-creation .ant-input-affix-wrapper:hover {
  border-color: #722ed1;
  box-shadow: 0 0 0 2px rgba(114, 46, 209, 0.1);
}

.robot-framework-test-creation .ant-btn-primary {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
  border: none;
  border-radius: 6px;
  height: 40px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(114, 46, 209, 0.2);
  transition: all 0.3s;
}

.robot-framework-test-creation .ant-btn-primary:hover {
  background: linear-gradient(135deg, #531dab 0%, #391085 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(114, 46, 209, 0.3);
}

.robot-framework-test-creation .ant-btn-primary:active {
  transform: translateY(0);
}

.robot-framework-test-creation .ant-btn {
  border-radius: 6px;
  height: 36px;
  font-weight: 500;
  transition: all 0.3s;
}

.robot-framework-test-creation .ant-btn:hover {
  transform: translateY(-1px);
}

.robot-framework-test-creation .ant-btn:active {
  transform: translateY(0);
}

.robot-framework-test-creation .ant-divider-horizontal.ant-divider-with-text {
  margin: 24px 0;
  font-weight: 600;
  color: #262626;
}

.robot-framework-test-creation .ant-divider-horizontal.ant-divider-with-text::before,
.robot-framework-test-creation .ant-divider-horizontal.ant-divider-with-text::after {
  border-top-color: #722ed1;
}

.robot-framework-test-creation .ant-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.robot-framework-test-creation .ant-card .ant-card-head-title {
  color: #262626;
  font-weight: 600;
}

.robot-framework-test-creation .ant-space {
  width: 100%;
}

.robot-framework-test-creation pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #2d3748;
  color: #e2e8f0;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.robot-framework-test-creation .ant-progress-line {
  margin: 16px 0;
}

.robot-framework-test-creation .ant-progress-bg {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
}

.robot-framework-test-creation .ant-alert {
  border-radius: 6px;
  margin-bottom: 16px;
}

.robot-framework-test-creation .ant-alert-info {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.robot-framework-test-creation .ant-alert-info .ant-alert-icon {
  color: #52c41a;
}

.robot-framework-test-creation .ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

.robot-framework-test-creation .ant-spin-container {
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .robot-framework-test-creation {
    padding: 8px;
  }
  
  .robot-framework-test-creation .ant-col {
    margin-bottom: 16px;
  }
  
  .robot-framework-test-creation .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .robot-framework-test-creation .ant-space {
    flex-direction: column;
  }
  
  .robot-framework-test-creation .ant-space > .ant-space-item {
    width: 100%;
  }
}

/* 动画效果 */
.robot-framework-test-creation .main-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.robot-framework-test-creation .ant-card:not(.main-card) {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 代码高亮样式 */
.robot-framework-test-creation pre {
  position: relative;
}

.robot-framework-test-creation pre::before {
  content: 'Robot Framework';
  position: absolute;
  top: 8px;
  right: 12px;
  background: rgba(114, 46, 209, 0.8);
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

/* 工具提示样式增强 */
.robot-framework-test-creation .ant-tooltip-inner {
  background: #2d3748;
  color: #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.robot-framework-test-creation .ant-tooltip-arrow::before {
  background: #2d3748;
}
