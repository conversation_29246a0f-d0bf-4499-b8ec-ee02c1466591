import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 全局样式
import '@/styles/index.scss'

// API 服务
import api from '@/services/api'

// 全局配置
Vue.config.productionTip = false

// 使用 Element UI
Vue.use(ElementUI)

// 全局挂载 API
Vue.prototype.$api = api

// 全局消息提示
Vue.prototype.$message = ElementUI.Message
Vue.prototype.$confirm = ElementUI.MessageBox.confirm
Vue.prototype.$alert = ElementUI.MessageBox.alert

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
