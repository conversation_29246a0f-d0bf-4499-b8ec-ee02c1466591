# 网页元素抓取工具依赖包

# Selenium WebDriver
selenium>=4.15.0

# WebDriver管理器 - 自动下载和管理ChromeDriver
webdriver-manager>=4.0.0

# 数据处理
dataclasses>=0.6  # Python 3.7+已内置，但为了兼容性列出

# 日志处理（Python内置，无需安装）
# logging

# JSON处理（Python内置，无需安装）
# json

# 时间处理（Python内置，无需安装）
# time

# 类型提示（Python内置，无需安装）
# typing

# 可选：更好的JSON处理
# ujson>=5.0.0

# 可选：更好的日志格式化
# colorlog>=6.0.0

# 可选：进度条显示
# tqdm>=4.60.0

# 可选：配置文件处理
# pyyaml>=6.0

# 可选：更好的错误处理和调试
# rich>=13.0.0
