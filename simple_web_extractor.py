#!/usr/bin/env python3
"""
简化版网页元素抓取工具
快速抓取网页中的可交互元素，输出基本信息
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

class SimpleWebExtractor:
    """简化版网页元素抓取器"""
    
    def __init__(self):
        self.driver = None
        
        # 定义要抓取的元素选择器
        self.selectors = {
            '按钮': ['button', 'input[type="button"]', 'input[type="submit"]', '[role="button"]'],
            '输入框': ['input[type="text"]', 'input[type="password"]', 'input[type="email"]', 'textarea'],
            '下拉选择': ['select'],
            '复选框': ['input[type="checkbox"]'],
            '单选框': ['input[type="radio"]'],
            '链接': ['a[href]']
        }
    
    def setup_driver(self):
        """设置Chrome驱动"""
        options = Options()
        options.add_argument('--headless')  # 无头模式
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=options)
    
    def close_driver(self):
        """关闭驱动"""
        if self.driver:
            self.driver.quit()
    
    def get_element_name(self, element):
        """获取元素名称"""
        # 尝试多种方式获取元素名称
        name_attrs = ['text', 'placeholder', 'value', 'title', 'aria-label', 'alt', 'id', 'name']
        
        for attr in name_attrs:
            if attr == 'text':
                name = element.text.strip()
            else:
                name = element.get_attribute(attr)
            
            if name and len(name.strip()) > 0:
                return name.strip()
        
        return '未命名元素'
    
    def generate_xpath(self, element):
        """生成简单的XPath"""
        element_id = element.get_attribute('id')
        if element_id:
            return f'//*[@id="{element_id}"]'
        
        element_class = element.get_attribute('class')
        if element_class:
            return f'//{element.tag_name}[@class="{element_class}"]'
        
        return f'//{element.tag_name}'
    
    def extract_elements(self, url):
        """抓取网页元素"""
        elements = []
        
        try:
            self.setup_driver()
            print(f"正在访问: {url}")
            
            self.driver.get(url)
            time.sleep(3)  # 等待页面加载
            
            # 遍历所有元素类型
            for element_type, selectors in self.selectors.items():
                for selector in selectors:
                    try:
                        found_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        
                        for element in found_elements:
                            if element.is_displayed() and element.is_enabled():
                                element_info = {
                                    '元素类型': element_type,
                                    '元素名称': self.get_element_name(element),
                                    'XPath': self.generate_xpath(element),
                                    'CSS选择器': selector,
                                    '标签名': element.tag_name,
                                    'ID': element.get_attribute('id') or '',
                                    'Class': element.get_attribute('class') or '',
                                    '文本内容': element.text.strip() if element.text else '',
                                    '是否可见': element.is_displayed(),
                                    '是否可用': element.is_enabled()
                                }
                                elements.append(element_info)
                    
                    except Exception as e:
                        continue
            
            print(f"成功抓取 {len(elements)} 个元素")
            
        except Exception as e:
            print(f"抓取失败: {e}")
        
        finally:
            self.close_driver()
        
        return elements
    
    def print_elements(self, elements):
        """打印元素信息"""
        if not elements:
            print("未找到任何元素")
            return
        
        print(f"\n找到 {len(elements)} 个可交互元素:")
        print("=" * 80)
        
        # 统计各类型元素数量
        type_counts = {}
        for element in elements:
            element_type = element['元素类型']
            type_counts[element_type] = type_counts.get(element_type, 0) + 1
        
        print("元素类型统计:")
        for element_type, count in type_counts.items():
            print(f"  {element_type}: {count} 个")
        
        print("\n详细元素列表:")
        print("-" * 80)
        
        for i, element in enumerate(elements, 1):
            print(f"{i}. 【{element['元素类型']}】{element['元素名称']}")
            print(f"   XPath: {element['XPath']}")
            print(f"   CSS: {element['CSS选择器']}")
            if element['ID']:
                print(f"   ID: {element['ID']}")
            if element['文本内容']:
                print(f"   文本: {element['文本内容']}")
            print()
    
    def save_to_json(self, elements, filename):
        """保存到JSON文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(elements, f, ensure_ascii=False, indent=2)
            print(f"元素数据已保存到: {filename}")
        except Exception as e:
            print(f"保存失败: {e}")
    
    def generate_robot_script(self, elements, filename):
        """生成Robot Framework脚本"""
        try:
            script = "*** Variables ***\n"
            script += "# 页面元素定位器\n"
            
            # 生成变量
            for i, element in enumerate(elements, 1):
                var_name = f"{element['元素类型'].upper()}_{i}"
                script += f"${{{var_name}}}    {element['XPath']}\n"
            
            script += "\n*** Test Cases ***\n"
            script += "页面元素交互测试\n"
            script += "    [Documentation]    自动生成的页面元素交互测试\n\n"
            
            # 生成测试步骤
            for i, element in enumerate(elements, 1):
                var_name = f"{element['元素类型'].upper()}_{i}"
                element_type = element['元素类型']
                element_name = element['元素名称']
                
                if element_type == '按钮':
                    script += f"    # 点击{element_name}\n"
                    script += f"    网页端 等待并点击元素    ${{{var_name}}}\n\n"
                elif element_type == '输入框':
                    script += f"    # 输入到{element_name}\n"
                    script += f"    网页端 等待并输入文本    ${{{var_name}}}    测试文本\n\n"
                elif element_type == '下拉选择':
                    script += f"    # 选择{element_name}\n"
                    script += f"    Select From List By Index    ${{{var_name}}}    1\n\n"
                elif element_type == '链接':
                    script += f"    # 点击链接{element_name}\n"
                    script += f"    网页端 等待并点击元素    ${{{var_name}}}\n\n"
                elif element_type in ['复选框', '单选框']:
                    script += f"    # 选择{element_name}\n"
                    script += f"    网页端 等待并点击元素    ${{{var_name}}}\n\n"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(script)
            
            print(f"Robot Framework脚本已保存到: {filename}")
            
        except Exception as e:
            print(f"生成Robot Framework脚本失败: {e}")

def main():
    """主函数"""
    print("🕷️ 简化版网页元素抓取工具")
    print("=" * 50)
    
    # 获取用户输入
    url = input("请输入要抓取的网页URL: ").strip()
    if not url:
        print("使用默认URL进行演示...")
        url = "https://www.baidu.com"
    
    # 创建抓取器
    extractor = SimpleWebExtractor()
    
    # 抓取元素
    elements = extractor.extract_elements(url)
    
    if elements:
        # 显示结果
        extractor.print_elements(elements)
        
        # 保存文件
        base_name = url.replace('https://', '').replace('http://', '').replace('/', '_').replace(':', '_')
        
        # 保存JSON
        json_file = f"elements_{base_name}.json"
        extractor.save_to_json(elements, json_file)
        
        # 生成Robot Framework脚本
        robot_file = f"test_{base_name}.robot"
        extractor.generate_robot_script(elements, robot_file)
        
        print(f"\n✅ 抓取完成!")
        print(f"📊 总计: {len(elements)} 个元素")
        print(f"📁 文件已保存:")
        print(f"  - JSON数据: {json_file}")
        print(f"  - Robot脚本: {robot_file}")
    
    else:
        print("❌ 未找到任何可交互元素")

if __name__ == "__main__":
    main()
