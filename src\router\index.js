import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/components/Layout/Layout.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/web/robot-framework-create',
    children: [
      {
        path: 'web/robot-framework-create',
        name: 'RobotFrameworkCreate',
        component: () => import('@/views/Web/RobotFramework/RobotFrameworkTestCreation.vue'),
        meta: {
          title: 'Robot Framework 测试创建'
        }
      }
    ]
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = to.meta.title + ' - UI Automation'
  }
  next()
})

export default router
