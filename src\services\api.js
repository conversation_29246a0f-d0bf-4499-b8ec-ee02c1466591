import axios from 'axios'
import { Message } from 'element-ui'

// 创建axios实例
const api = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    console.log('收到响应:', response.status, response.config.url)
    return response.data
  },
  error => {
    // 对响应错误做点什么
    console.error('响应错误:', error)

    let errorMessage = '请求失败'

    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response
      errorMessage = data?.message || data?.detail || `服务器错误 (${status})`
    } else if (error.request) {
      // 请求已发出但没有收到响应
      errorMessage = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    Message.error(errorMessage)
    return Promise.reject(error)
  }
)

// Robot Framework API
export const robotFrameworkApi = {
  // 分析页面
  analyze(data) {
    console.log('robotFrameworkApi.analyze 调用，输入数据:', data)
    const formData = new FormData()

    // 添加基本参数
    formData.append('analysis_type', data.analysis_type || 'comprehensive')
    formData.append('save_to_database', data.save_to_database !== undefined ? data.save_to_database : 'false')
    formData.append('category', data.category || 'robot_framework')
    formData.append('priority', data.priority || '1')

    // 添加页面信息
    if (data.page_url) {
      formData.append('page_url', data.page_url)
    }
    if (data.image_url) {
      formData.append('image_url', data.image_url)
    }

    // 添加描述信息
    if (data.interaction_description) {
      formData.append('interaction_description', data.interaction_description)
    }
    if (data.additional_context) {
      formData.append('additional_context', data.additional_context)
    }
    if (data.script_description) {
      formData.append('script_description', data.script_description)
    }

    // 添加文件
    if (data.file) {
      formData.append('file', data.file)
    }

    // 添加标签
    if (data.tags) {
      formData.append('tags', JSON.stringify(data.tags))
    } else {
      formData.append('tags', JSON.stringify(['robot_framework', 'web_test']))
    }

    // 调试：打印FormData内容
    console.log('FormData内容:')
    for (const [key, value] of formData.entries()) {
      console.log(`${key}:`, value)
    }

    return api.post('/v1/web/robot-framework/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export default api
