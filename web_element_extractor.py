#!/usr/bin/env python3
"""
网页元素抓取工具类
用于抓取网页中可交互的元素，包括按钮、输入框、下拉选择等
每个元素包含类型、名称、XPath等信息
"""

import time
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class WebElement:
    """网页元素数据类"""
    element_type: str          # 元素类型：button, input, select, link, etc.
    element_name: str          # 元素名称
    xpath: str                 # 元素XPath
    css_selector: str          # CSS选择器
    element_id: Optional[str] = None           # 元素ID
    element_class: Optional[str] = None        # 元素class
    tag_name: Optional[str] = None             # 标签名
    text_content: Optional[str] = None         # 文本内容
    placeholder: Optional[str] = None          # placeholder属性
    value: Optional[str] = None                # value属性
    href: Optional[str] = None                 # href属性（链接）
    is_visible: bool = True                    # 是否可见
    is_enabled: bool = True                    # 是否可用
    position: Optional[Dict[str, int]] = None  # 元素位置
    size: Optional[Dict[str, int]] = None      # 元素大小

class WebElementExtractor:
    """网页元素抓取工具类"""
    
    def __init__(self, headless: bool = True, timeout: int = 10):
        """
        初始化抓取工具
        
        Args:
            headless: 是否使用无头模式
            timeout: 页面加载超时时间
        """
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
        # 定义可交互元素的选择器
        self.interactive_selectors = {
            'button': [
                'button',
                'input[type="button"]',
                'input[type="submit"]',
                'input[type="reset"]',
                '[role="button"]',
                '.btn',
                '.button'
            ],
            'input': [
                'input[type="text"]',
                'input[type="password"]',
                'input[type="email"]',
                'input[type="number"]',
                'input[type="tel"]',
                'input[type="url"]',
                'input[type="search"]',
                'input[type="date"]',
                'input[type="time"]',
                'input[type="datetime-local"]',
                'input[type="month"]',
                'input[type="week"]',
                'input[type="color"]',
                'input[type="range"]',
                'input[type="file"]',
                'textarea'
            ],
            'checkbox': [
                'input[type="checkbox"]'
            ],
            'radio': [
                'input[type="radio"]'
            ],
            'select': [
                'select'
            ],
            'link': [
                'a[href]'
            ],
            'image': [
                'img[onclick]',
                'img[role="button"]'
            ]
        }
    
    def _setup_driver(self) -> None:
        """设置Chrome驱动"""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            # 自动下载并设置ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            logger.info("Chrome驱动设置成功")
            
        except Exception as e:
            logger.error(f"Chrome驱动设置失败: {e}")
            raise
    
    def _close_driver(self) -> None:
        """关闭驱动"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Chrome驱动已关闭")
            except Exception as e:
                logger.error(f"关闭Chrome驱动失败: {e}")
    
    def _generate_xpath(self, element) -> str:
        """生成元素的XPath"""
        try:
            # 使用JavaScript生成XPath
            xpath_script = """
            function getXPath(element) {
                if (element.id !== '') {
                    return '//*[@id="' + element.id + '"]';
                }
                if (element === document.body) {
                    return '/html/body';
                }
                
                var ix = 0;
                var siblings = element.parentNode.childNodes;
                for (var i = 0; i < siblings.length; i++) {
                    var sibling = siblings[i];
                    if (sibling === element) {
                        var tagName = element.tagName.toLowerCase();
                        var parentXPath = getXPath(element.parentNode);
                        return parentXPath + '/' + tagName + '[' + (ix + 1) + ']';
                    }
                    if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                        ix++;
                    }
                }
            }
            return getXPath(arguments[0]);
            """
            return self.driver.execute_script(xpath_script, element)
        except Exception:
            # 备用方案：简单XPath生成
            tag_name = element.tag_name.lower()
            element_id = element.get_attribute('id')
            if element_id:
                return f'//*[@id="{element_id}"]'
            
            element_class = element.get_attribute('class')
            if element_class:
                classes = element_class.split()
                if classes:
                    return f'//{tag_name}[@class="{element_class}"]'
            
            return f'//{tag_name}'
    
    def _get_element_name(self, element, element_type: str) -> str:
        """获取元素名称"""
        # 优先级：text > placeholder > value > title > aria-label > id > class
        name_sources = [
            element.text.strip() if element.text else '',
            element.get_attribute('placeholder') or '',
            element.get_attribute('value') or '',
            element.get_attribute('title') or '',
            element.get_attribute('aria-label') or '',
            element.get_attribute('alt') or '',  # 图片alt属性
            element.get_attribute('id') or '',
            element.get_attribute('name') or ''
        ]
        
        # 找到第一个非空的名称
        for name in name_sources:
            if name and len(name.strip()) > 0:
                return name.strip()
        
        # 如果都没有，根据元素类型生成默认名称
        default_names = {
            'button': '按钮',
            'input': '输入框',
            'select': '下拉选择',
            'checkbox': '复选框',
            'radio': '单选框',
            'link': '链接',
            'image': '图片'
        }
        
        return default_names.get(element_type, '未知元素')
    
    def _extract_element_info(self, element, element_type: str) -> WebElement:
        """提取单个元素的详细信息"""
        try:
            # 基本信息
            xpath = self._generate_xpath(element)
            element_name = self._get_element_name(element, element_type)
            
            # CSS选择器生成
            css_selector = ''
            element_id = element.get_attribute('id')
            if element_id:
                css_selector = f'#{element_id}'
            else:
                element_class = element.get_attribute('class')
                if element_class:
                    classes = '.'.join(element_class.split())
                    css_selector = f'{element.tag_name.lower()}.{classes}'
                else:
                    css_selector = element.tag_name.lower()
            
            # 位置和大小信息
            location = element.location
            size = element.size
            
            # 创建WebElement对象
            web_element = WebElement(
                element_type=element_type,
                element_name=element_name,
                xpath=xpath,
                css_selector=css_selector,
                element_id=element_id,
                element_class=element.get_attribute('class'),
                tag_name=element.tag_name.lower(),
                text_content=element.text.strip() if element.text else None,
                placeholder=element.get_attribute('placeholder'),
                value=element.get_attribute('value'),
                href=element.get_attribute('href'),
                is_visible=element.is_displayed(),
                is_enabled=element.is_enabled(),
                position=location,
                size=size
            )
            
            return web_element
            
        except Exception as e:
            logger.error(f"提取元素信息失败: {e}")
            return None
    
    def extract_elements(self, url: str) -> List[WebElement]:
        """
        抓取网页中的所有可交互元素
        
        Args:
            url: 目标网页URL
            
        Returns:
            List[WebElement]: 提取的元素列表
        """
        elements = []
        
        try:
            # 设置驱动
            self._setup_driver()
            
            # 访问网页
            logger.info(f"正在访问网页: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(2)
            
            # 等待页面完全加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # 遍历所有元素类型
            for element_type, selectors in self.interactive_selectors.items():
                for selector in selectors:
                    try:
                        # 查找元素
                        found_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        
                        for element in found_elements:
                            # 只处理可见且可用的元素
                            if element.is_displayed() and element.is_enabled():
                                web_element = self._extract_element_info(element, element_type)
                                if web_element:
                                    elements.append(web_element)
                                    
                    except Exception as e:
                        logger.warning(f"处理选择器 {selector} 时出错: {e}")
                        continue
            
            logger.info(f"成功提取 {len(elements)} 个可交互元素")
            
        except TimeoutException:
            logger.error(f"页面加载超时: {url}")
        except WebDriverException as e:
            logger.error(f"WebDriver错误: {e}")
        except Exception as e:
            logger.error(f"抓取元素时发生错误: {e}")
        finally:
            # 关闭驱动
            self._close_driver()
        
        return elements
    
    def save_to_json(self, elements: List[WebElement], filename: str) -> None:
        """
        将提取的元素保存为JSON文件
        
        Args:
            elements: 元素列表
            filename: 保存的文件名
        """
        try:
            elements_data = [asdict(element) for element in elements]
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(elements_data, f, ensure_ascii=False, indent=2)
            logger.info(f"元素数据已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存JSON文件失败: {e}")
    
    def print_elements_summary(self, elements: List[WebElement]) -> None:
        """打印元素摘要信息"""
        if not elements:
            print("未找到任何可交互元素")
            return
        
        print(f"\n找到 {len(elements)} 个可交互元素:")
        print("=" * 80)
        
        # 按类型分组统计
        type_counts = {}
        for element in elements:
            type_counts[element.element_type] = type_counts.get(element.element_type, 0) + 1
        
        print("元素类型统计:")
        for element_type, count in type_counts.items():
            print(f"  {element_type}: {count} 个")
        
        print("\n详细元素列表:")
        print("-" * 80)
        
        for i, element in enumerate(elements, 1):
            print(f"{i}. 类型: {element.element_type}")
            print(f"   名称: {element.element_name}")
            print(f"   XPath: {element.xpath}")
            print(f"   CSS选择器: {element.css_selector}")
            if element.element_id:
                print(f"   ID: {element.element_id}")
            if element.text_content:
                print(f"   文本: {element.text_content}")
            print()

    def filter_elements(self, elements: List[WebElement], **filters) -> List[WebElement]:
        """
        根据条件过滤元素

        Args:
            elements: 元素列表
            **filters: 过滤条件
                - element_type: 元素类型
                - visible_only: 仅可见元素
                - enabled_only: 仅可用元素
                - has_text: 包含文本的元素
                - has_id: 包含ID的元素

        Returns:
            List[WebElement]: 过滤后的元素列表
        """
        filtered = elements

        if filters.get('element_type'):
            filtered = [e for e in filtered if e.element_type == filters['element_type']]

        if filters.get('visible_only', False):
            filtered = [e for e in filtered if e.is_visible]

        if filters.get('enabled_only', False):
            filtered = [e for e in filtered if e.is_enabled]

        if filters.get('has_text', False):
            filtered = [e for e in filtered if e.text_content and e.text_content.strip()]

        if filters.get('has_id', False):
            filtered = [e for e in filtered if e.element_id]

        return filtered

    def get_elements_by_type(self, elements: List[WebElement]) -> Dict[str, List[WebElement]]:
        """
        按类型分组元素

        Args:
            elements: 元素列表

        Returns:
            Dict[str, List[WebElement]]: 按类型分组的元素字典
        """
        grouped = {}
        for element in elements:
            if element.element_type not in grouped:
                grouped[element.element_type] = []
            grouped[element.element_type].append(element)
        return grouped

    def generate_robot_framework_keywords(self, elements: List[WebElement]) -> str:
        """
        为提取的元素生成Robot Framework关键字

        Args:
            elements: 元素列表

        Returns:
            str: Robot Framework关键字代码
        """
        rf_code = "*** Variables ***\n"
        rf_code += "# 页面元素定位器\n"

        # 生成变量定义
        for i, element in enumerate(elements):
            var_name = f"{element.element_type.upper()}_{i+1}"
            rf_code += f"${{{var_name}}}    {element.xpath}\n"

        rf_code += "\n*** Test Cases ***\n"
        rf_code += "页面元素交互测试\n"
        rf_code += "    [Documentation]    测试页面元素交互功能\n"
        rf_code += "    [Tags]    ui    interaction\n\n"

        # 生成测试步骤
        for i, element in enumerate(elements):
            var_name = f"{element.element_type.upper()}_{i+1}"

            if element.element_type == 'button':
                rf_code += f"    # 点击{element.element_name}\n"
                rf_code += f"    网页端 等待并点击元素    ${{{var_name}}}\n\n"
            elif element.element_type == 'input':
                rf_code += f"    # 输入到{element.element_name}\n"
                rf_code += f"    网页端 等待并输入文本    ${{{var_name}}}    测试文本\n\n"
            elif element.element_type == 'select':
                rf_code += f"    # 选择{element.element_name}\n"
                rf_code += f"    Select From List By Index    ${{{var_name}}}    1\n\n"
            elif element.element_type == 'link':
                rf_code += f"    # 点击链接{element.element_name}\n"
                rf_code += f"    网页端 等待并点击元素    ${{{var_name}}}\n\n"

        return rf_code

def main():
    """主函数 - 使用示例"""
    print("🕷️ 网页元素抓取工具")
    print("=" * 50)

    # 获取用户输入的URL
    url = input("请输入要抓取的网页URL: ").strip()
    if not url:
        url = "https://www.baidu.com"  # 默认URL
        print(f"使用默认URL: {url}")

    # 创建抓取工具实例
    extractor = WebElementExtractor(headless=False, timeout=15)

    try:
        print(f"\n🔍 正在抓取网页: {url}")
        print("-" * 50)

        # 抓取元素
        elements = extractor.extract_elements(url)

        if not elements:
            print("❌ 未找到任何可交互元素")
            return

        # 打印摘要
        extractor.print_elements_summary(elements)

        # 按类型分组显示
        grouped_elements = extractor.get_elements_by_type(elements)
        print(f"\n📊 按类型分组的元素:")
        print("-" * 50)
        for element_type, type_elements in grouped_elements.items():
            print(f"\n{element_type.upper()} ({len(type_elements)} 个):")
            for i, element in enumerate(type_elements[:5], 1):  # 只显示前5个
                print(f"  {i}. {element.element_name} - {element.xpath}")
            if len(type_elements) > 5:
                print(f"  ... 还有 {len(type_elements) - 5} 个")

        # 保存到JSON文件
        safe_filename = url.replace('https://', '').replace('http://', '').replace('/', '_').replace(':', '_')
        json_filename = f"elements_{safe_filename}.json"
        extractor.save_to_json(elements, json_filename)

        # 生成Robot Framework代码
        rf_code = extractor.generate_robot_framework_keywords(elements)
        rf_filename = f"robot_framework_{safe_filename}.robot"
        with open(rf_filename, 'w', encoding='utf-8') as f:
            f.write(rf_code)
        print(f"📝 Robot Framework代码已保存到: {rf_filename}")

        # 显示统计信息
        print(f"\n📈 抓取统计:")
        print(f"  - 总元素数: {len(elements)}")
        print(f"  - 可见元素: {len([e for e in elements if e.is_visible])}")
        print(f"  - 可用元素: {len([e for e in elements if e.is_enabled])}")
        print(f"  - 有文本元素: {len([e for e in elements if e.text_content])}")
        print(f"  - 有ID元素: {len([e for e in elements if e.element_id])}")

        print(f"\n✅ 抓取完成!")

    except Exception as e:
        print(f"❌ 抓取失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
