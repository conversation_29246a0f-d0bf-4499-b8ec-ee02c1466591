# 🚀 快速启动指南

## 📋 环境检查

确保您的系统满足以下要求：
- **Node.js**: >= 14.0.0
- **npm**: >= 6.0.0

```bash
# 检查版本
node --version
npm --version
```

## 🛠️ 快速开始

### 方式1: 使用设置脚本（推荐）

```bash
# 1. 进入项目目录
cd frontend-vue2

# 2. 运行设置脚本
npm run setup

# 3. 启动开发服务器
npm run serve
```

### 方式2: 手动安装

```bash
# 1. 进入项目目录
cd frontend-vue2

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run serve
```

## 🌐 访问应用

启动成功后，在浏览器中访问：
- **开发服务器**: http://localhost:3000
- **Robot Framework页面**: http://localhost:3000/web/robot-framework-create

## 📝 可用命令

```bash
npm run serve      # 启动开发服务器 (推荐)
npm run dev        # 启动开发服务器 (别名)
npm run build      # 构建生产版本
npm run lint       # 代码检查
npm run setup      # 项目初始化
```

## 🎯 功能说明

### Robot Framework 测试创建
1. **页面URL** - 输入要测试的网页地址（必填）
2. **页面截图** - 上传页面截图辅助分析（可选）
3. **测试描述** - 描述测试场景和步骤（可选）
4. **附加上下文** - 提供额外的测试信息（可选）
5. **实时分析** - 查看分析过程和结果
6. **脚本生成** - 获取最终的Robot Framework脚本

## 🔧 配置说明

### API配置
- **开发环境**: `http://localhost:8000/api`
- **生产环境**: `/api`

⚠️ **重要**: 确保后端服务运行在8000端口

### 端口配置
- **前端服务**: 3000端口
- **后端API**: 8000端口

## 🚨 常见问题

### 1. 端口被占用
```
Vue CLI会自动选择其他可用端口，请查看终端输出的实际端口号
```

### 2. 依赖安装失败
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules
npm install
```

### 3. Node.js版本过低
```
请升级到Node.js 14或更高版本
下载地址: https://nodejs.org/
```

### 4. API连接失败
检查项目：
- [ ] 后端服务是否在8000端口运行
- [ ] 网络连接是否正常
- [ ] 防火墙是否阻止连接

## 📁 项目结构

```
frontend-vue2/
├── src/
│   ├── components/         # 公共组件
│   ├── views/             # 页面组件
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── services/          # API服务
│   └── styles/            # 样式文件
├── public/                # 静态资源
├── scripts/               # 工具脚本
└── 配置文件...
```

## 🎉 成功启动标志

看到以下信息表示启动成功：
```
  App running at:
  - Local:   http://localhost:3000/
  - Network: http://192.168.x.x:3000/
```

现在您可以开始使用Vue2 + Element-UI前端应用了！
