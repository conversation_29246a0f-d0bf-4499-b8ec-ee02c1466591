{"name": "frontend", "version": "1.0.0", "description": "Vue2 + Element-UI Frontend Application", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve --mode development", "build:prod": "vue-cli-service build --mode production", "setup": "node scripts/setup.js"}, "dependencies": {"vue": "^2.6.14", "vue-router": "^3.5.4", "vuex": "^3.6.2", "element-ui": "^2.15.13", "axios": "^1.4.0", "core-js": "^3.31.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^6.1.0", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^8.7.1", "sass": "^1.62.1", "sass-loader": "^13.3.2"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}