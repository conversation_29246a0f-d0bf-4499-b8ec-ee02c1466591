2025-07-02 14:30:25 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_web_agents:209 | 开始注册Web平台智能体...
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: 图片分析智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: 页面分析智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: image_description_generator
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: 页面分析存储智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: YAML生成智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: YAML脚本执行智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: Playwright代码生成智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: Playwright执行智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: 脚本数据库保存智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_agent:191 | 注册智能体成功: 测试用例元素解析智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_web_agents:288 | Web平台智能体注册完成，共注册 10 个智能体
2025-07-02 14:30:25 | INFO     | app.agents.factory:register_stream_collector:378 | 流式响应收集器注册成功
2025-07-02 14:30:25 | INFO     | app.core.agents.base:__init__:38 | 初始化 图片描述生成智能体 智能体 (ID: image_description_generator)
2025-07-02 14:30:25 | INFO     | app.agents.web.image_description_agent:__init__:44 | 图片描述生成智能体初始化完成，用户反馈: False
2025-07-02 14:30:25 | INFO     | app.agents.factory:create_agent:145 | 创建智能体: image_description_generator
2025-07-02 14:30:25 | INFO     | app.agents.factory:create_assistant_agent:108 | 创建 AssistantAgent: description_generator (模型: uitars)
2025-07-02 14:30:25 | INFO     | app.agents.web.image_description_agent:_convert_image_to_agimage:253 | 成功转换图片为AGImage，尺寸: (2880, 1800)
2025-07-02 14:30:46 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [图片描述生成智能体] operation 耗时: 21.12秒
2025-07-02 14:30:46 | INFO     | app.agents.factory:clear_registered_agents:422 | 已清空智能体注册记录
2025-07-03 10:55:47 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 10:55:47 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 10:55:47 | INFO     | app.core.agents.base:__init__:38 | 初始化 图片描述生成智能体 智能体 (ID: image_description_generator)
2025-07-03 10:55:47 | INFO     | app.agents.web.image_description_agent:__init__:44 | 图片描述生成智能体初始化完成，用户反馈: False
2025-07-03 10:55:47 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: image_description_generator
2025-07-03 10:55:47 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: description_generator (模型: uitars)
2025-07-03 10:55:48 | INFO     | app.agents.web.image_description_agent:_convert_image_to_agimage:253 | 成功转换图片为AGImage，尺寸: (2880, 1800)
2025-07-03 10:56:04 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [图片描述生成智能体] operation 耗时: 16.14秒
2025-07-03 10:56:04 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 11:02:15 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 11:02:15 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 11:02:15 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 11:02:15 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 11:02:15 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 11:02:15 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: deepseek)
2025-07-03 11:02:15 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: deepseek)
2025-07-03 11:03:06 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:342 | 团队分析执行失败: APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1584, in request
    raise self._make_status_error_from_response(err.response) from None

openai.APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}

2025-07-03 11:03:06 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 51.22秒
2025-07-03 11:03:06 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 11:10:56 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 11:10:56 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 11:10:56 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 11:10:56 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 11:10:56 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 11:10:56 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: deepseek)
2025-07-03 11:10:56 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: deepseek)
2025-07-03 11:11:43 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:342 | 团队分析执行失败: APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1584, in request
    raise self._make_status_error_from_response(err.response) from None

openai.APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}

2025-07-03 11:11:43 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 46.58秒
2025-07-03 11:13:07 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 11:22:03 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 11:22:03 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 11:22:03 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 11:22:03 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 11:22:03 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 11:22:03 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: deepseek)
2025-07-03 11:22:03 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: deepseek)
2025-07-03 11:22:44 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:342 | 团队分析执行失败: APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\futures.py", line 284, in __await__
    yield self  # This tells Task to wait for completion.

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 304, in __wakeup
    future.result()

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\futures.py", line 201, in result
    raise self._exception

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\tasks.py", line 232, in __step
    result = coro.send(None)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1584, in request
    raise self._make_status_error_from_response(err.response) from None

openai.APIStatusError: Error code: 402 - {'error': {'message': 'Insufficient Balance', 'type': 'unknown_error', 'param': None, 'code': 'invalid_request_error'}}

2025-07-03 11:22:44 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 40.60秒
2025-07-03 11:23:26 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 11:27:43 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 11:27:43 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 11:27:43 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 11:27:43 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 11:27:43 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 11:27:43 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 11:27:43 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 11:30:10 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 147.62秒
2025-07-03 11:30:10 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 14:07:56 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 14:07:56 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 14:07:56 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 14:07:56 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 14:07:56 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 14:07:56 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 14:07:56 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 14:10:57 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 180.49秒
2025-07-03 14:11:51 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 14:15:48 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 14:15:48 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 14:15:48 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 14:15:48 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 14:15:48 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 14:15:48 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 14:15:48 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 14:18:14 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 146.21秒
2025-07-03 14:18:14 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 14:21:08 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 14:21:08 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 14:21:08 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 14:21:08 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 14:21:08 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 14:21:08 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 14:21:08 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 14:24:09 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 181.06秒
2025-07-03 14:24:51 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
