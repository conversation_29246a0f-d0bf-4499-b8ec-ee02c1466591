2025-07-03 14:30:57 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 14:30:57 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 14:30:57 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 14:30:57 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 14:30:57 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 14:30:57 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 14:30:57 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 14:33:28 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 150.54秒
2025-07-03 14:33:28 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 14:39:29 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 14:39:29 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 14:39:29 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 14:39:29 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 14:39:29 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 14:39:29 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 14:39:29 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 14:42:20 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 171.08秒
2025-07-03 14:42:20 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 14:46:14 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 14:46:14 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 14:46:14 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 14:46:14 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 14:46:14 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 14:46:14 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 14:46:14 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 14:48:41 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 146.82秒
2025-07-03 14:48:41 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 14:51:26 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 14:51:26 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 14:51:26 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 14:51:26 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 14:51:26 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 14:51:26 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 14:51:26 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 14:54:06 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 160.24秒
2025-07-03 14:54:06 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 14:58:03 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 14:58:03 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 14:58:03 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 14:58:03 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 14:58:03 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 14:58:03 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 14:58:03 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 15:00:29 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 145.71秒
2025-07-03 15:00:29 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 15:41:53 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 15:41:53 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 15:41:53 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 15:41:53 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 15:41:53 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 15:41:53 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 15:41:53 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 15:43:19 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 15:43:19 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 15:43:19 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 15:43:19 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 15:43:19 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 15:43:19 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 15:43:19 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 15:46:09 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 169.89秒
2025-07-03 15:46:09 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 16:04:16 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 16:04:16 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 16:04:16 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 16:04:16 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 16:04:16 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 16:04:16 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 16:04:16 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 16:04:16 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_create_rf_analysis_team:178 | 创建Robot Framework分析团队失败: Graph must have at least one start node
2025-07-03 16:04:16 | ERROR    | app.core.agents.base:handle_exception:126 | [Robot Framework分析智能体] 在handle_message中发生错误: Graph must have at least one start node
2025-07-03 16:04:16 | ERROR    | app.core.agents.base:send_error:109 | [Robot Framework分析智能体] 错误: 在handle_message中发生错误: Graph must have at least one start node
2025-07-03 16:04:16 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 16:10:34 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 16:10:34 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 16:10:34 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 16:10:34 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 16:10:34 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 16:10:34 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 16:10:34 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 16:13:14 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 160.36秒
2025-07-03 16:13:14 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 16:15:57 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 16:15:57 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 16:15:57 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 16:15:57 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 16:15:57 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 16:15:57 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 16:15:57 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 16:18:09 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 131.83秒
2025-07-03 16:18:09 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 16:25:08 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 16:25:08 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 16:25:08 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 16:25:08 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 16:25:08 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 16:25:08 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 16:25:08 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 16:25:08 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_fetch_page_content:293 | 抓取页面内容失败: HTTPSConnectionPool(host='www.baidu.com', port=443): Max retries exceeded with url: / (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F06A8D2620>: Failed to resolve 'www.baidu.com' ([Errno 11001] getaddrinfo failed)"))
2025-07-03 16:25:10 | ERROR    | app.agents.web.robot_framework_analyzer_agent:_run_team_analysis:349 | 团队分析执行失败: APIConnectionError: Connection error.
Traceback:
Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc

httpcore.ConnectError: [Errno 11001] getaddrinfo failed


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1519, in request
    response = await self._client.send(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc

httpx.ConnectError: [Errno 11001] getaddrinfo failed


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\teams\_group_chat\_chat_agent_container.py", line 79, in handle_request
    async for msg in self._agent.on_messages_stream(self._message_buffer, ctx.cancellation_token):

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 852, in on_messages_stream
    async for inference_output in self._call_llm(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_agentchat\agents\_assistant_agent.py", line 965, in _call_llm
    async for chunk in model_client.create_stream(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 824, in create_stream
    async for chunk in chunks:

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\autogen_ext\models\openai\_openai_client.py", line 1015, in _create_stream_chunks
    stream = await stream_future

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1784, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)

  File "D:\AI\testing-main-0628\003\ui-automation\backend\.venv\lib\site-packages\openai\_base_client.py", line 1551, in request
    raise APIConnectionError(request=request) from err

openai.APIConnectionError: Connection error.

2025-07-03 16:25:10 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 1.42秒
2025-07-03 16:25:10 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 16:25:24 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 16:25:24 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 16:25:24 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 16:25:24 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 16:25:24 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 16:25:24 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 16:25:24 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 16:27:35 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 131.48秒
2025-07-03 16:27:35 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
2025-07-03 16:39:55 | INFO     | app.core.agents.collector:__init__:38 | web流式响应收集器初始化完成
2025-07-03 16:39:55 | INFO     | app.agents.factory:register_web_agents:211 | 开始注册Web平台智能体...
2025-07-03 16:39:55 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 图片分析智能体
2025-07-03 16:39:55 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析智能体
2025-07-03 16:39:55 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: image_description_generator
2025-07-03 16:39:55 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 页面分析存储智能体
2025-07-03 16:39:55 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML生成智能体
2025-07-03 16:39:55 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: YAML脚本执行智能体
2025-07-03 16:39:55 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright代码生成智能体
2025-07-03 16:39:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Playwright执行智能体
2025-07-03 16:39:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 脚本数据库保存智能体
2025-07-03 16:39:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: 测试用例元素解析智能体
2025-07-03 16:39:56 | INFO     | app.agents.factory:register_agent:193 | 注册智能体成功: Robot Framework分析智能体
2025-07-03 16:39:56 | INFO     | app.agents.factory:register_web_agents:299 | Web平台智能体注册完成，共注册 11 个智能体
2025-07-03 16:39:56 | INFO     | app.agents.factory:register_stream_collector:389 | 流式响应收集器注册成功
2025-07-03 16:39:56 | INFO     | app.core.agents.base:__init__:38 | 初始化 Robot Framework分析智能体 智能体 (ID: robot_framework_analyzer)
2025-07-03 16:39:56 | INFO     | app.agents.web.robot_framework_analyzer_agent:__init__:52 | Robot Framework分析智能体初始化完成，用户反馈: False
2025-07-03 16:39:56 | INFO     | app.agents.factory:create_agent:147 | 创建智能体: Robot Framework分析智能体
2025-07-03 16:39:56 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_page_element_analyzer (模型: qwenvl)
2025-07-03 16:39:56 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_interaction_flow_analyzer (模型: qwenvl)
2025-07-03 16:39:56 | INFO     | app.agents.factory:create_assistant_agent:110 | 创建 AssistantAgent: rf_script_generator (模型: qwenvl)
2025-07-03 16:43:06 | INFO     | app.core.agents.base:end_performance_monitoring:179 | [Robot Framework分析智能体] operation 耗时: 190.68秒
2025-07-03 16:43:06 | INFO     | app.agents.factory:clear_registered_agents:433 | 已清空智能体注册记录
