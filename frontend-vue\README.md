# Frontend - Vue2 + Element-UI

基于Vue2和Element-UI架构的前端工程，用于UI自动化测试平台。

## 技术栈

- **Vue 2.6.14** - 渐进式JavaScript框架
- **Element-UI 2.15.13** - 基于Vue 2.0的桌面端组件库
- **Vue Router 3.x** - Vue.js官方路由管理器
- **Vuex 3.x** - Vue.js的状态管理模式
- **Axios** - 基于Promise的HTTP库
- **Sass** - CSS预处理器

## 环境要求

- **Node.js**: >= 14.0.0
- **npm**: >= 6.0.0

## 项目结构

```
frontend-vue/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 公共组件
│   │   ├── Layout/         # 布局组件
│   │   └── StreamingDisplay/ # 流式显示组件
│   ├── views/              # 页面组件
│   │   └── Web/            # Web测试相关页面
│   ├── router/             # 路由配置
│   ├── store/              # Vuex状态管理
│   ├── services/           # API服务
│   ├── styles/             # 全局样式
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── .env.development        # 开发环境配置
├── .env.production         # 生产环境配置
├── vue.config.js           # Vue CLI配置
└── package.json            # 项目依赖
```

## 安装依赖

```bash
npm install
```

## 开发

```bash
# 启动开发服务器
npm run serve

# 或者使用
npm run dev
```

开发服务器将在 http://localhost:3000 启动

## 构建

```bash
# 生产环境构建
npm run build

# 或者
npm run build:prod
```

## 功能特性

### Robot Framework 测试创建
- 页面URL输入（必填）
- 页面截图上传（可选，支持清空）
- 测试描述输入（可选）
- 附加上下文输入（可选）
- 实时流式显示分析结果
- 最终结果展示

### 流式显示组件
- 支持Server-Sent Events (SSE)
- 实时消息显示
- 自动滚动到底部
- 错误处理和重连机制
- 支持自定义流式端点

## API集成

项目配置了与后端API的集成：
- 开发环境: `http://localhost:8000/api`
- 生产环境: `/api`

## 样式系统

- 使用Sass作为CSS预处理器
- 定义了完整的设计变量系统
- 提供了常用的工具类
- 自定义了Element-UI主题

## 浏览器支持

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79
