#!/usr/bin/env python3
"""
测试Robot Framework智能体的图文结合分析功能
验证页面元素分析助手能够正确处理图片和页面内容
"""

import asyncio
import json
import base64
from pathlib import Path
from datetime import datetime

def test_image_analysis_integration():
    """测试图文结合分析集成"""
    print("🧪 Robot Framework图文结合分析测试")
    print("验证页面元素分析助手的图片处理能力")
    print("=" * 60)
    
    try:
        # 测试导入
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        from app.core.messages.web import RobotFrameworkAnalysisRequest
        from app.core.agents import StreamResponseCollector
        from app.core.types import AgentPlatform
        from autogen_core import Image as AGImage
        from autogen_agentchat.messages import MultiModalMessage
        from PIL import Image
        
        print("✅ 所有必要模块导入成功")
        
        # 测试图片转换功能
        print("\n🖼️ 测试图片转换功能")
        print("-" * 40)
        
        # 创建测试用的base64图片数据（1x1像素的透明PNG）
        test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        
        # 测试请求对象创建
        test_requests = [
            {
                "name": "图文结合分析请求",
                "data": {
                    "session_id": "test-image-001",
                    "analysis_type": "comprehensive",
                    "script_name": "login_with_image_test",
                    "interaction_description": "分析登录页面的所有元素，结合截图验证元素位置",
                    "page_url": "https://example.com/login",
                    "image_data": f"data:image/png;base64,{test_image_base64}",
                    "additional_context": "重点关注表单元素的视觉布局"
                }
            },
            {
                "name": "纯图片分析请求",
                "data": {
                    "session_id": "test-image-002",
                    "analysis_type": "element_analysis",
                    "script_name": "image_only_test",
                    "interaction_description": "仅基于页面截图识别所有可交互元素",
                    "image_data": f"data:image/png;base64,{test_image_base64}"
                }
            },
            {
                "name": "图片URL分析请求",
                "data": {
                    "session_id": "test-image-003",
                    "analysis_type": "element_analysis",
                    "script_name": "url_image_test",
                    "interaction_description": "从URL获取页面截图并分析元素",
                    "image_url": "https://via.placeholder.com/800x600.png?text=Test+Page"
                }
            }
        ]
        
        print("📋 创建测试请求:")
        for i, test_case in enumerate(test_requests, 1):
            try:
                request = RobotFrameworkAnalysisRequest(**test_case["data"])
                print(f"  ✅ {i}. {test_case['name']}")
                print(f"     - 会话ID: {request.session_id}")
                print(f"     - 分析类型: {request.analysis_type}")
                print(f"     - 图片数据: {'有' if request.image_data else '无'}")
                print(f"     - 图片URL: {'有' if request.image_url else '无'}")
                
            except Exception as e:
                print(f"  ❌ {i}. {test_case['name']} - 创建失败: {e}")
                return False
        
        print(f"\n✅ 所有请求模型创建测试通过!")
        
        # 测试智能体创建
        print("\n🤖 测试Robot Framework智能体创建")
        print("-" * 40)
        
        collector = StreamResponseCollector(platform=AgentPlatform.WEB)
        rf_agent = RobotFrameworkAnalyzerAgent(collector=collector)
        print("✅ Robot Framework分析智能体创建成功")
        
        # 测试页面元素分析助手创建
        page_analyzer = RobotFrameworkAnalyzerAgent.create_page_element_analyzer_agent()
        print("✅ 页面元素分析助手创建成功")
        print(f"   - 智能体名称: {page_analyzer.name}")
        print(f"   - 模型类型: qwenvl (视觉模型)")
        
        # 测试提示词
        print("\n📄 测试页面元素分析助手提示词")
        print("-" * 40)
        
        prompt = RobotFrameworkAnalyzerAgent._build_page_element_analyzer_prompt()
        print(f"✅ 提示词构建成功")
        print(f"   - 长度: {len(prompt)} 字符")
        print(f"   - 行数: {len(prompt.split('\\n'))}")
        
        # 验证关键功能点
        key_features = [
            "图文结合分析",
            "视觉验证",
            "精确匹配",
            "MultiModalMessage",
            "AGImage"
        ]
        
        print(f"   - 关键功能验证:")
        for feature in key_features:
            if feature in prompt:
                print(f"     ✅ {feature}")
            else:
                print(f"     ❌ {feature} - 缺失")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_image_conversion():
    """测试图片转换功能"""
    print("\n🔄 测试图片转换功能")
    print("-" * 40)
    
    try:
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        from app.core.messages.web import RobotFrameworkAnalysisRequest
        from app.core.agents import StreamResponseCollector
        from app.core.types import AgentPlatform
        
        # 创建智能体
        collector = StreamResponseCollector(platform=AgentPlatform.WEB)
        rf_agent = RobotFrameworkAnalyzerAgent(collector=collector)
        
        # 创建测试请求（包含base64图片）
        test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        
        request = RobotFrameworkAnalysisRequest(
            session_id="test-conversion-001",
            analysis_type="element_analysis",
            script_name="conversion_test",
            interaction_description="测试图片转换功能",
            image_data=f"data:image/png;base64,{test_image_base64}"
        )
        
        print("📝 测试请求创建成功")
        print(f"   - 图片数据长度: {len(request.image_data)} 字符")
        
        # 测试图片转换
        try:
            ag_image = await rf_agent._convert_image_to_agimage(request)
            print("✅ 图片转换成功")
            print(f"   - AGImage对象类型: {type(ag_image)}")
            
        except Exception as e:
            print(f"❌ 图片转换失败: {e}")
            return False
        
        # 测试任务准备（包含图片）
        try:
            task_message = await rf_agent._prepare_analysis_task(request)
            print("✅ 任务准备成功")
            print(f"   - 消息类型: {type(task_message)}")
            
            # 检查是否为多模态消息
            from autogen_agentchat.messages import MultiModalMessage
            if isinstance(task_message, MultiModalMessage):
                print("   - ✅ 创建了多模态消息")
                print(f"   - 内容项数: {len(task_message.content)}")
            else:
                print("   - ❌ 未创建多模态消息")
                return False
                
        except Exception as e:
            print(f"❌ 任务准备失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 图片转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_analysis():
    """测试提示词分析功能"""
    print("\n📊 测试提示词分析功能")
    print("-" * 40)
    
    try:
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        
        # 获取页面元素分析助手提示词
        prompt = RobotFrameworkAnalyzerAgent._build_page_element_analyzer_prompt()
        
        # 分析提示词内容
        analysis_modes = [
            "仅图片分析模式",
            "仅页面内容分析模式", 
            "图文结合分析模式"
        ]
        
        image_features = [
            "视觉识别",
            "图像分析",
            "视觉验证",
            "精确匹配",
            "图文结合"
        ]
        
        xpath_strategies = [
            "ID定位",
            "Name定位",
            "文本定位",
            "属性组合",
            "视觉位置",
            "相对定位"
        ]
        
        print("📋 提示词内容分析:")
        print(f"   - 总长度: {len(prompt)} 字符")
        print(f"   - 总行数: {len(prompt.split('\\n'))}")
        
        print(f"\\n   - 分析模式覆盖:")
        for mode in analysis_modes:
            if mode in prompt:
                print(f"     ✅ {mode}")
            else:
                print(f"     ❌ {mode}")
        
        print(f"\\n   - 图像功能覆盖:")
        for feature in image_features:
            if feature in prompt:
                print(f"     ✅ {feature}")
            else:
                print(f"     ❌ {feature}")
        
        print(f"\\n   - XPath策略覆盖:")
        for strategy in xpath_strategies:
            if strategy in prompt:
                print(f"     ✅ {strategy}")
            else:
                print(f"     ❌ {strategy}")
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词分析失败: {e}")
        return False

async def main():
    """主函数"""
    print("🧪 Robot Framework图文结合分析功能测试")
    print("验证页面元素分析助手的图片处理和分析能力")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("基础集成测试", test_image_analysis_integration),
        ("图片转换测试", test_image_conversion),
        ("提示词分析测试", test_prompt_analysis)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 图文结合分析功能测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有图文结合分析功能测试通过!")
        print("\n📋 功能清单:")
        print("  - ✅ 多模态消息创建和处理")
        print("  - ✅ Base64图片数据转换")
        print("  - ✅ 图片URL获取和处理")
        print("  - ✅ AGImage对象转换")
        print("  - ✅ 图文结合分析提示词")
        print("  - ✅ 视觉验证和精确匹配策略")
        print("  - ✅ 多种XPath生成策略")
        
        print("\n🚀 Robot Framework页面元素分析助手已支持图文结合分析!")
        print("📍 主要特性:")
        print("   - 同时处理页面截图和HTML内容")
        print("   - 通过视觉验证确保XPath准确性")
        print("   - 提供多种定位策略和备用方案")
        print("   - 支持动态元素和响应式布局分析")
    else:
        print("⚠️  部分图文结合分析功能测试失败，请检查相关模块")
    
    print("\n🏁 Robot Framework图文结合分析功能测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
