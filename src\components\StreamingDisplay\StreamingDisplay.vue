<template>
  <div class="streaming-display">
    <el-card class="streaming-card">
      <div slot="header" class="card-header">
        <span>实时分析过程</span>
        <div class="header-actions">
          <el-button
            v-if="messages.length > 0"
            size="mini"
            @click="clearMessages"
            icon="el-icon-delete"
          >
            清空
          </el-button>
          <div class="connection-status">
            <i
              :class="connectionStatusIcon"
              :style="{ color: connectionStatusColor }"
            ></i>
            {{ connectionStatusText }}
          </div>
        </div>
      </div>

      <div class="messages-container" ref="messagesContainer">
        <div
          v-for="(message, index) in messages"
          :key="index"
          class="message-item"
          :class="{ 'is-final': message.is_final }"
        >
          <div class="message-time">
            {{ formatTime(message.timestamp) }}
          </div>
          <div class="message-content">
            {{ message.content }}
          </div>
        </div>

        <div v-if="messages.length === 0 && isActive" class="empty-message">
          <i class="el-icon-loading"></i>
          等待分析结果...
        </div>

        <div v-if="messages.length === 0 && !isActive" class="empty-message">
          暂无分析数据
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'StreamingDisplay',

  props: {
    sessionId: {
      type: String,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    },
    streamEndpoint: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      messages: [],
      eventSource: null,
      connectionStatus: 'disconnected', // disconnected, connecting, connected, error
      reconnectAttempts: 0,
      maxReconnectAttempts: 5,
      reconnectDelay: 1000
    }
  },

  computed: {
    connectionStatusIcon() {
      const icons = {
        disconnected: 'el-icon-close',
        connecting: 'el-icon-loading',
        connected: 'el-icon-success',
        error: 'el-icon-warning'
      }
      return icons[this.connectionStatus] || 'el-icon-close'
    },

    connectionStatusColor() {
      const colors = {
        disconnected: '#909399',
        connecting: '#409EFF',
        connected: '#67C23A',
        error: '#F56C6C'
      }
      return colors[this.connectionStatus] || '#909399'
    },

    connectionStatusText() {
      const texts = {
        disconnected: '未连接',
        connecting: '连接中',
        connected: '已连接',
        error: '连接错误'
      }
      return texts[this.connectionStatus] || '未知状态'
    }
  },

  watch: {
    sessionId: {
      immediate: true,
      handler(newSessionId, oldSessionId) {
        if (newSessionId && newSessionId !== oldSessionId) {
          this.connectToStream()
        }
      }
    },

    isActive(newValue) {
      if (!newValue && this.eventSource) {
        this.disconnectFromStream()
      }
    }
  },

  beforeDestroy() {
    this.disconnectFromStream()
  },

  methods: {
    connectToStream() {
      if (!this.sessionId) return

      this.disconnectFromStream()
      this.connectionStatus = 'connecting'

      const endpoint = this.streamEndpoint || `/v1/web/robot-framework/stream/${this.sessionId}`
      const baseUrl = process.env.VUE_APP_API_BASE_URL || '/api'

      // 构建完整的URL
      let url
      if (endpoint.startsWith('/api') && baseUrl === '/api') {
        // 如果endpoint已经包含/api且baseUrl也是/api，直接使用endpoint
        url = endpoint
      } else if (baseUrl.endsWith('/api') && endpoint.startsWith('/api')) {
        // 避免重复的/api
        url = `${baseUrl.slice(0, -4)}${endpoint}`
      } else {
        // 正常拼接
        url = `${baseUrl}${endpoint}`
      }

      console.log('连接到流式端点:', url)

      try {
        this.eventSource = new EventSource(url)

        this.eventSource.onopen = () => {
          console.log('流式连接已建立')
          this.connectionStatus = 'connected'
          this.reconnectAttempts = 0
        }

        this.eventSource.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            console.log('收到流式数据:', data)

            const message = {
              content: data.content || data.message || '无内容',
              timestamp: new Date(),
              is_final: data.is_final || false
            }

            this.messages.push(message)
            this.$nextTick(() => {
              this.scrollToBottom()
            })

            // 触发分析完成事件
            this.$emit('analysis-complete', data)

            // 如果是最终消息，触发最终完成事件
            if (data.is_final) {
              this.$emit('final-complete', data)
            }
          } catch (error) {
            console.error('解析流式数据失败:', error)
            this.$emit('error', '数据解析失败')
          }
        }

        this.eventSource.onerror = (error) => {
          console.error('流式连接错误:', error)
          this.connectionStatus = 'error'
          this.$emit('error', '连接错误')

          // 尝试重连
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            setTimeout(() => {
              this.reconnectAttempts++
              console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
              this.connectToStream()
            }, this.reconnectDelay * this.reconnectAttempts)
          }
        }
      } catch (error) {
        console.error('创建EventSource失败:', error)
        this.connectionStatus = 'error'
        this.$emit('error', '无法建立连接')
      }
    },

    disconnectFromStream() {
      if (this.eventSource) {
        this.eventSource.close()
        this.eventSource = null
        this.connectionStatus = 'disconnected'
        console.log('流式连接已断开')
      }
    },

    clearMessages() {
      this.messages = []
    },

    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },

    formatTime(timestamp) {
      return timestamp.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.streaming-display {
  .streaming-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .connection-status {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #909399;

          i {
            font-size: 14px;
          }
        }
      }
    }

    .messages-container {
      max-height: 400px;
      overflow-y: auto;
      padding: 0;

      .message-item {
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &.is-final {
          background-color: #f0f9ff;
          border-left: 3px solid #409EFF;
        }

        .message-time {
          font-size: 11px;
          color: #909399;
          margin-bottom: 4px;
        }

        .message-content {
          font-size: 13px;
          line-height: 1.4;
          color: #303133;
          white-space: pre-wrap;
          word-break: break-word;
        }
      }

      .empty-message {
        text-align: center;
        padding: 40px 20px;
        color: #909399;
        font-size: 14px;

        i {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
