import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    // 全局加载状态
    loading: false,
    // 用户信息
    userInfo: null,
    // 当前分析会话
    currentSession: null
  },
  
  getters: {
    isLoading: state => state.loading,
    getUserInfo: state => state.userInfo,
    getCurrentSession: state => state.currentSession
  },
  
  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading
    },
    
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
    },
    
    SET_CURRENT_SESSION(state, session) {
      state.currentSession = session
    }
  },
  
  actions: {
    setLoading({ commit }, loading) {
      commit('SET_LOADING', loading)
    },
    
    setUserInfo({ commit }, userInfo) {
      commit('SET_USER_INFO', userInfo)
    },
    
    setCurrentSession({ commit }, session) {
      commit('SET_CURRENT_SESSION', session)
    }
  },
  
  modules: {
    // 可以在这里添加模块
  }
})
