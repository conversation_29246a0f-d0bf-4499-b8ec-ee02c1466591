#!/usr/bin/env python3
"""
测试Robot Framework脚本生成助手的Variables生成功能
验证脚本生成助手可以适当生成Variables部分，不再生成Keywords部分
"""

import asyncio
import json
from datetime import datetime

def test_script_generator_variables_support():
    """测试脚本生成助手的Variables支持"""
    print("🧪 Robot Framework脚本生成助手Variables生成测试")
    print("验证脚本生成助手可以适当生成Variables部分")
    print("=" * 60)
    
    try:
        # 测试导入
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        
        print("✅ Robot Framework分析智能体导入成功")
        
        # 获取脚本生成助手提示词
        print("\n📄 测试脚本生成助手提示词")
        print("-" * 40)
        
        prompt = RobotFrameworkAnalyzerAgent._build_script_generator_prompt()
        print(f"✅ 脚本生成助手提示词构建成功")
        print(f"   - 长度: {len(prompt)} 字符")
        print(f"   - 行数: {len(prompt.split('\\n'))}")
        
        # 验证Variables支持
        print(f"\\n🔧 验证Variables生成支持:")
        
        variables_features = [
            ("Variables部分支持", "Variables部分"),
            ("适当生成说明", "适当生成"),
            ("变量定义", "变量定义"),
            ("重复数据管理", "重复使用的数据"),
            ("元素定位器管理", "元素定位器"),
            ("测试数据配置", "测试数据"),
            ("Variables示例", "*** Variables ***"),
            ("URL变量", "${LOGIN_URL}"),
            ("用户名变量", "${USERNAME}"),
            ("定位器变量", "${USERNAME_INPUT}")
        ]
        
        for feature_name, feature_text in variables_features:
            if feature_text in prompt:
                print(f"   ✅ {feature_name}")
            else:
                print(f"   ❌ {feature_name} - 缺失")
        
        # 验证不再生成Keywords
        print(f"\\n🚫 验证不再生成Keywords:")
        
        keywords_prohibition = [
            ("禁止Keywords部分", "不需要生成 Keywords 部分" in prompt or "不需要Keywords 部分" in prompt),
            ("保持脚本简洁", "保持脚本简洁" in prompt),
            ("直接编写操作", "直接在测试用例中编写" in prompt),
            ("无Keywords示例", "*** Keywords ***" not in prompt or prompt.count("*** Keywords ***") == 0)
        ]
        
        for prohibition_name, prohibition_check in keywords_prohibition:
            if prohibition_check:
                print(f"   ✅ {prohibition_name}")
            else:
                print(f"   ❌ {prohibition_name}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_variables_generation_examples():
    """测试Variables生成示例"""
    print("\\n📝 测试Variables生成示例")
    print("-" * 40)
    
    try:
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        
        prompt = RobotFrameworkAnalyzerAgent._build_script_generator_prompt()
        
        # 检查示例中的Variables部分
        print(f"📋 验证示例脚本中的Variables:")
        
        example_checks = [
            ("包含Variables部分", "*** Variables ***"),
            ("URL变量定义", "${LOGIN_URL}"),
            ("用户名变量定义", "${USERNAME}"),
            ("密码变量定义", "${PASSWORD}"),
            ("无效用户变量", "${INVALID_USER}"),
            ("错误密码变量", "${WRONG_PASSWORD}"),
            ("用户名输入框定位器", "${USERNAME_INPUT}"),
            ("密码输入框定位器", "${PASSWORD_INPUT}"),
            ("登录按钮定位器", "${LOGIN_BUTTON}"),
            ("欢迎消息定位器", "${WELCOME_MSG}"),
            ("错误消息定位器", "${ERROR_MSG}"),
            ("变量注释", "# 页面元素定位器"),
            ("测试用例使用变量", "网页端 打开浏览器    ${LOGIN_URL}"),
            ("输入操作使用变量", "网页端 等待并输入文本    ${USERNAME_INPUT}    ${USERNAME}")
        ]
        
        for check_name, check_text in example_checks:
            if check_text in prompt:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name} - 缺失")
        
        # 验证不包含Keywords
        print(f"\\n🚫 验证不包含Keywords:")
        
        keywords_absence_checks = [
            ("无Keywords部分", "*** Keywords ***" not in prompt),
            ("无自定义关键字", "执行用户登录" not in prompt),
            ("无关键字参数", "[Arguments]" not in prompt),
            ("无关键字文档", "[Documentation]" not in prompt or prompt.count("[Documentation]") <= 3)  # 只有测试用例的文档
        ]
        
        for check_name, check_result in keywords_absence_checks:
            if check_result:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
        
        # 统计Variables相关内容
        variables_count = prompt.count("Variables")
        variable_definitions = prompt.count("${")
        
        print(f"\\n📊 Variables内容统计:")
        print(f"   - 'Variables'出现次数: {variables_count}")
        print(f"   - 变量定义数量: {variable_definitions}")
        
        return True
        
    except Exception as e:
        print(f"❌ Variables生成示例测试失败: {e}")
        return False

def test_variables_generation_guidelines():
    """测试Variables生成指导原则"""
    print("\\n📖 测试Variables生成指导原则")
    print("-" * 40)
    
    try:
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        
        prompt = RobotFrameworkAnalyzerAgent._build_script_generator_prompt()
        
        # 验证生成指导原则
        print(f"📋 验证Variables生成指导原则:")
        
        guidelines = [
            ("生成条件说明", "何时生成 Variables"),
            ("重复数据识别", "相同数据在多个测试用例中重复使用"),
            ("元素定位器管理", "页面元素定位器需要统一管理"),
            ("测试数据配置", "测试数据需要集中配置"),
            ("维护便利性", "便于后续维护和修改"),
            ("命名规范", "使用大写字母和下划线的描述性名称"),
            ("变量分类", "按功能分组"),
            ("变量注释", "为变量组添加注释说明"),
            ("URL类变量", "URL类"),
            ("元素定位器类", "元素定位器类"),
            ("测试数据类", "测试数据类")
        ]
        
        for guideline_name, guideline_text in guidelines:
            if guideline_text in prompt:
                print(f"   ✅ {guideline_name}")
            else:
                print(f"   ❌ {guideline_name} - 缺失")
        
        # 验证模板使用中的变量支持
        print(f"\\n🔧 验证模板使用中的变量支持:")
        
        template_variable_checks = [
            ("点击操作变量示例", "${LOGIN_BUTTON}"),
            ("输入操作变量示例", "${USERNAME_INPUT}    ${USERNAME}"),
            ("打开浏览器变量示例", "${LOGIN_URL}"),
            ("模板变量说明", "可使用变量")
        ]
        
        for check_name, check_text in template_variable_checks:
            if check_text in prompt:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name} - 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ Variables生成指导原则测试失败: {e}")
        return False

def test_script_structure_balance():
    """测试脚本结构平衡性"""
    print("\\n⚖️ 测试脚本结构平衡性")
    print("-" * 40)
    
    try:
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        
        prompt = RobotFrameworkAnalyzerAgent._build_script_generator_prompt()
        
        # 验证结构平衡
        print(f"📋 验证脚本结构平衡:")
        
        balance_checks = [
            ("支持Variables生成", "Variables部分" in prompt and "适当生成" in prompt),
            ("禁止Keywords生成", "不需要生成 Keywords 部分" in prompt or "不需要Keywords 部分" in prompt),
            ("保持简洁原则", "简洁" in prompt),
            ("提高可维护性", "可维护性" in prompt),
            ("统一管理数据", "统一管理" in prompt),
            ("集中配置", "集中配置" in prompt)
        ]
        
        for balance_name, balance_check in balance_checks:
            if balance_check:
                print(f"   ✅ {balance_name}")
            else:
                print(f"   ❌ {balance_name}")
        
        # 验证结构组成
        print(f"\\n🏗️ 验证脚本结构组成:")
        
        structure_checks = [
            ("Settings部分", "Settings部分"),
            ("Variables部分", "Variables部分"),
            ("Test Cases部分", "Test Cases部分"),
            ("无Keywords部分", "Keywords部分" not in prompt or prompt.count("Keywords部分") == 0)
        ]
        
        for structure_name, structure_check in structure_checks:
            if isinstance(structure_check, str):
                if structure_check in prompt:
                    print(f"   ✅ {structure_name}")
                else:
                    print(f"   ❌ {structure_name} - 缺失")
            else:
                if structure_check:
                    print(f"   ✅ {structure_name}")
                else:
                    print(f"   ❌ {structure_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 脚本结构平衡性测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🧪 Robot Framework脚本生成助手Variables生成功能测试")
    print("验证脚本生成助手支持Variables部分，不再生成Keywords部分")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("脚本生成助手Variables支持测试", test_script_generator_variables_support),
        ("Variables生成示例测试", test_variables_generation_examples),
        ("Variables生成指导原则测试", test_variables_generation_guidelines),
        ("脚本结构平衡性测试", test_script_structure_balance)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\\n" + "=" * 60)
    print("📊 Variables生成功能测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    print(f"\\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有Variables生成功能测试通过!")
        print("\\n📋 Variables生成特性:")
        print("  - ✅ 支持Variables部分的适当生成")
        print("  - ✅ 不再生成Keywords部分")
        print("  - ✅ 支持重复数据的变量化管理")
        print("  - ✅ 支持页面元素定位器的统一管理")
        print("  - ✅ 支持测试数据的集中配置")
        print("  - ✅ 提供Variables生成指导原则")
        print("  - ✅ 保持代码简洁和可维护性")
        print("  - ✅ 包含完整的Variables示例")
        
        print("\\n🚀 Robot Framework脚本生成助手Variables功能已启用!")
        print("📍 主要改进:")
        print("   - 启用了Variables部分的生成")
        print("   - 禁用了Keywords部分的生成")
        print("   - 添加了Variables生成的指导原则")
        print("   - 提供了变量使用的完整示例")
        print("   - 支持数据和定位器的统一管理")
        print("   - 保持了脚本的简洁性和可维护性")
    else:
        print("⚠️  部分Variables生成功能测试失败，请检查相关配置")
    
    print("\\n🏁 Robot Framework Variables生成功能测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
