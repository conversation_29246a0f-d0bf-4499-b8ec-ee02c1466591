#!/usr/bin/env python3
"""
测试Robot Framework脚本生成助手的Keywords生成功能
验证脚本生成助手可以适当生成Keywords部分
"""

import asyncio
import json
from datetime import datetime

def test_script_generator_keywords_support():
    """测试脚本生成助手的Keywords支持"""
    print("🧪 Robot Framework脚本生成助手Keywords生成测试")
    print("验证脚本生成助手可以适当生成Keywords部分")
    print("=" * 60)
    
    try:
        # 测试导入
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        
        print("✅ Robot Framework分析智能体导入成功")
        
        # 获取脚本生成助手提示词
        print("\n📄 测试脚本生成助手提示词")
        print("-" * 40)
        
        prompt = RobotFrameworkAnalyzerAgent._build_script_generator_prompt()
        print(f"✅ 脚本生成助手提示词构建成功")
        print(f"   - 长度: {len(prompt)} 字符")
        print(f"   - 行数: {len(prompt.split('\\n'))}")
        
        # 验证Keywords支持
        print(f"\\n🔧 验证Keywords生成支持:")
        
        keywords_features = [
            ("Keywords部分支持", "Keywords部分"),
            ("适当生成说明", "适当生成"),
            ("自定义关键字", "自定义关键字"),
            ("重复操作封装", "重复操作"),
            ("复杂流程封装", "复杂流程"),
            ("参数化支持", "参数化"),
            ("代码复用", "复用"),
            ("Keywords示例", "*** Keywords ***"),
            ("执行用户登录", "执行用户登录"),
            ("验证登录成功", "验证登录成功")
        ]
        
        for feature_name, feature_text in keywords_features:
            if feature_text in prompt:
                print(f"   ✅ {feature_name}")
            else:
                print(f"   ❌ {feature_name} - 缺失")
        
        # 验证Keywords生成指导
        print(f"\\n📋 验证Keywords生成指导:")
        
        guidance_features = [
            ("何时生成Keywords", "何时生成 Keywords"),
            ("Keywords命名规范", "Keywords 命名"),
            ("Keywords参数使用", "Keywords 参数"),
            ("Keywords文档说明", "Keywords 文档"),
            ("重复操作识别", "重复操作序列"),
            ("业务流程封装", "业务流程需要封装"),
            ("参数化操作", "参数化的操作")
        ]
        
        for guidance_name, guidance_text in guidance_features:
            if guidance_text in prompt:
                print(f"   ✅ {guidance_name}")
            else:
                print(f"   ❌ {guidance_name} - 缺失")
        
        # 验证不再禁止Keywords
        print(f"\\n🚫 验证不再禁止Keywords:")
        
        prohibition_checks = [
            ("不再完全禁止Keywords", "不需要Keywords 部分" not in prompt or "不需要生成 Keywords 部分" not in prompt),
            ("允许适当生成", "适当生成" in prompt),
            ("有生成条件说明", "何时生成" in prompt)
        ]
        
        for check_name, check_result in prohibition_checks:
            if check_result:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_script_generator_agent_creation():
    """测试支持Keywords的脚本生成助手创建"""
    print("\\n🤖 测试支持Keywords的脚本生成助手创建")
    print("-" * 40)
    
    try:
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        
        # 创建脚本生成助手
        script_generator = RobotFrameworkAnalyzerAgent.create_script_generator_agent()
        print("✅ 脚本生成助手智能体创建成功")
        print(f"   - 智能体名称: {script_generator.name}")
        print(f"   - 模型类型: qwenvl")
        
        # 验证系统消息
        system_message = script_generator._system_messages[0].content if script_generator._system_messages else ""
        print(f"   - 系统消息长度: {len(system_message)} 字符")
        
        # 检查系统消息中的Keywords相关要求
        keywords_requirements = [
            "Keywords部分",
            "适当生成",
            "重复操作",
            "复杂流程",
            "参数化",
            "何时生成 Keywords"
        ]
        
        print(f"   - Keywords支持验证:")
        for requirement in keywords_requirements:
            if requirement in system_message:
                print(f"     ✅ {requirement}")
            else:
                print(f"     ❌ {requirement}")
        
        return True
        
    except Exception as e:
        print(f"❌ 支持Keywords的脚本生成助手创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_keywords_generation_examples():
    """测试Keywords生成示例"""
    print("\\n📝 测试Keywords生成示例")
    print("-" * 40)
    
    try:
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        
        prompt = RobotFrameworkAnalyzerAgent._build_script_generator_prompt()
        
        # 检查示例中的Keywords部分
        print(f"📋 验证示例脚本中的Keywords:")
        
        example_checks = [
            ("包含Keywords部分", "*** Keywords ***"),
            ("执行用户登录关键字", "执行用户登录"),
            ("验证登录成功关键字", "验证登录成功"),
            ("关键字参数使用", "[Arguments]"),
            ("关键字文档", "[Documentation]"),
            ("参数化示例", "${username}"),
            ("标准模板使用", "网页端 等待并输入文本"),
            ("测试用例调用关键字", "执行用户登录    admin    password123")
        ]
        
        for check_name, check_text in example_checks:
            if check_text in prompt:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name} - 缺失")
        
        # 统计Keywords相关内容
        keywords_count = prompt.count("Keywords")
        custom_keywords_count = prompt.count("执行用户登录") + prompt.count("验证登录成功")
        
        print(f"\\n📊 Keywords内容统计:")
        print(f"   - 'Keywords'出现次数: {keywords_count}")
        print(f"   - 自定义关键字示例数: {custom_keywords_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Keywords生成示例测试失败: {e}")
        return False

def test_keywords_generation_guidelines():
    """测试Keywords生成指导原则"""
    print("\\n📖 测试Keywords生成指导原则")
    print("-" * 40)
    
    try:
        from app.agents.web.robot_framework_analyzer_agent import RobotFrameworkAnalyzerAgent
        
        prompt = RobotFrameworkAnalyzerAgent._build_script_generator_prompt()
        
        # 验证生成指导原则
        print(f"📋 验证Keywords生成指导原则:")
        
        guidelines = [
            ("生成条件说明", "何时生成 Keywords"),
            ("重复操作识别", "相同操作序列在多个测试用例中重复出现"),
            ("复杂流程封装", "复杂的业务流程需要封装"),
            ("参数化需求", "需要参数化的操作"),
            ("命名规范", "使用动词开头的中文描述性名称"),
            ("参数使用", "合理使用参数提高复用性"),
            ("文档说明", "为复杂关键字添加文档说明"),
            ("避免过度抽象", "保持脚本简洁" in prompt or "避免过度抽象" in prompt)
        ]
        
        for guideline_name, guideline_text in guidelines:
            if isinstance(guideline_text, str):
                if guideline_text in prompt:
                    print(f"   ✅ {guideline_name}")
                else:
                    print(f"   ❌ {guideline_name} - 缺失")
            else:
                # 布尔检查
                if guideline_text:
                    print(f"   ✅ {guideline_name}")
                else:
                    print(f"   ❌ {guideline_name}")
        
        # 验证平衡性
        print(f"\\n⚖️ 验证生成策略平衡性:")
        
        balance_checks = [
            ("支持Keywords生成", "Keywords部分" in prompt and "适当生成" in prompt),
            ("有生成条件限制", "何时生成" in prompt),
            ("保持简洁原则", "简洁" in prompt),
            ("避免过度复杂", "过度" in prompt or "复杂" in prompt)
        ]
        
        for balance_name, balance_check in balance_checks:
            if balance_check:
                print(f"   ✅ {balance_name}")
            else:
                print(f"   ❌ {balance_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Keywords生成指导原则测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🧪 Robot Framework脚本生成助手Keywords生成功能测试")
    print("验证脚本生成助手可以适当生成Keywords部分")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("脚本生成助手Keywords支持测试", test_script_generator_keywords_support),
        ("支持Keywords的智能体创建测试", test_script_generator_agent_creation),
        ("Keywords生成示例测试", test_keywords_generation_examples),
        ("Keywords生成指导原则测试", test_keywords_generation_guidelines)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\\n" + "=" * 60)
    print("📊 Keywords生成功能测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    print(f"\\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有Keywords生成功能测试通过!")
        print("\\n📋 Keywords生成特性:")
        print("  - ✅ 不再完全禁止Keywords部分生成")
        print("  - ✅ 可以适当生成自定义关键字")
        print("  - ✅ 支持重复操作的封装")
        print("  - ✅ 支持复杂业务流程的封装")
        print("  - ✅ 支持参数化的关键字")
        print("  - ✅ 提供Keywords生成指导原则")
        print("  - ✅ 保持代码简洁和可维护性")
        print("  - ✅ 包含完整的Keywords示例")
        
        print("\\n🚀 Robot Framework脚本生成助手Keywords功能已启用!")
        print("📍 主要改进:")
        print("   - 移除了对Keywords部分的完全禁止")
        print("   - 添加了Keywords生成的指导原则")
        print("   - 提供了自定义关键字的示例")
        print("   - 支持重复操作和复杂流程的封装")
        print("   - 保持了代码简洁性和可维护性的平衡")
    else:
        print("⚠️  部分Keywords生成功能测试失败，请检查相关配置")
    
    print("\\n🏁 Robot Framework Keywords生成功能测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
